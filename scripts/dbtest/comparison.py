#!/usr/bin/env python3

import typer
import pandas as pd

from sqlalchemy import create_engine, text

"""
Usage:
./comparison.py bodyworks
./comparison.py bodywork-notes
./comparison.py brake
./comparison.py component-notes
"""

host = "*************"
user = "berke"
password = "PsV3rvZ8w80VZ69g"
database = "dbtest_db"
port = "53306"

app = typer.Typer()


def connect_db(host, user, password, database, port):
    try:
        engine = create_engine(
            "mysql+mysqlconnector://" + user + ":" + password + "@" + host + ":" + port + "/" + database)
        connection = engine.connect()
        return connection
    except Exception as e:
        print(f"Veritabanı bağlantısı başarısız: {e}")
        return None


def close_db():
    """Close the db connection"""
    global connection
    if connection:
        connection.close()


connection = connect_db(host, user, password, database, port)


def compare_tables(sql_source, sql_target, connection):
    source_table_df = pd.read_sql(sql_source, connection)
    target_table_df = pd.read_sql(sql_target, connection)

    # print(source_table_df)
    # print(target_table_df)
    # exit()

    differences = pd.concat([source_table_df, target_table_df]).drop_duplicates(keep=False)

    if differences.empty:
        message = typer.style(f"Tüm veriler tutarlı!", fg=typer.colors.GREEN, bold=True)
        typer.echo(message)
    else:
        message = typer.style(f"Tutarsızlıklar bulundu: \n {differences}", fg=typer.colors.RED, bold=True)
        typer.echo(message)


def get_check_ids(limit : 10):
    sql = text(f"SELECT id FROM expertises WHERE old_id IS NOT NULL ORDER BY RAND() LIMIT {limit};")
    check_table_df = pd.read_sql(sql, connection)
    check_ids = check_table_df['id'].tolist()
    check_ids_str = ','.join(map(str, check_ids))

    message = typer.style(f"Check IDs: {check_ids_str}", fg=typer.colors.CYAN, bold=True)
    typer.echo(message)

    return check_ids_str


def get_check_uuids():
    sql = text("SELECT uuid FROM expertises WHERE old_id IS NOT NULL ORDER BY RAND() LIMIT 10;")
    check_table_df = pd.read_sql(sql, connection)
    check_ids = check_table_df['uuid'].tolist()
    check_ids_str = '","'.join(map(str, check_ids))
    check_ids_str = f'"{check_ids_str}"'

    message = typer.style(f"Check IDs: {check_ids_str}", fg=typer.colors.CYAN, bold=True)
    typer.echo(message)

    return check_ids_str


@app.command()
def bodyworks(limit: int = 10):
    """Bodyworks tablosunu test et."""

    print("bodyworks tablosu test ediliyor...")

    # engine = connect_db(host, user, password, database, port)

    # Debug
    # kaynak_df = pd.read_sql(text("SELECT * FROM t440_kaporta limit 100"), engine.connect())
    # print(kaynak_df)
    # exit()

    # kaynak_df = pd.read_sql(text("""SELECT t440_id FROM t440_kaporta JOIN expertises ON expertises.uuid = t440_kaporta.T440_servis_UQ
    #      JOIN t400_srvbaslik ON t400_srvbaslik.T400_UQ = t440_kaporta.T440_servis_UQ
    #      JOIN t116_testalanlari ON t116_testalanlari.T116_kod = t440_kaporta.T440_alan_kodu LIMIT 10"""), engine.connect())
    # print(kaynak_df)
    # exit()

    # with engine.connect() as connection:
        # # Check total count
    # source_table = connection.execute(text("""SELECT count(t440_id) FROM t440_kaporta JOIN t400_srvbaslik ON t400_srvbaslik.t400_uq = t440_kaporta.t440_servis_uq"""))
    # source_count = source_table.fetchone()
    #
    # target_table = connection.execute(text("SELECT count(expertise_bodyworks.id) FROM expertise_bodyworks LEFT JOIN expertises ON expertises.id = expertise_bodyworks.expertise_id WHERE expertises.old_id IS NOT NULL"))
    # target_count = target_table.fetchone()
    #
    # if source_count[0] != target_count[0]:
    #     message = typer.style(f"Toplam kayıt sayısı farklı! Kaynak: {source_count[0]} - Hedef: {target_count[0]} - Fark {source_count[0] - target_count[0]}", fg=typer.colors.RED, bold=True)
    #     typer.echo(message)
    # else:
    #     message = typer.style(f"Toplam kayıt sayısı aynı! Kaynak: {source_count[0]} - Hedef: {target_count[0]}", fg=typer.colors.GREEN, bold=True)
    #     typer.echo(message)

        # # Check total amount
    # source_table = connection.execute(text("SELECT SUM(T440_orjinal) FROM t440_kaporta"))
    # source_total = source_table.fetchone()
    #
    # target_table = connection.execute(text("SELECT SUM(orijinal) FROM expertise_bodyworks"))
    # target_total = target_table.fetchone()
    #
    # if source_total[0] != target_total[0]:
    #     message = typer.style(f"Toplam miktar farklı! Kaynak: {source_total[0]} - Hedef: {target_total[0]}", fg=typer.colors.RED, bold=True)
    #     typer.echo(message)
    # else:
    #     message = typer.style(f"Toplam miktar aynı! Kaynak: {source_total[0]} - Hedef: {target_total[0]}", fg=typer.colors.GREEN, bold=True)
    #     typer.echo(message)

        # fetch 100 records from source table and then check if they exist in target table
    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT expertises.id as expertise_id,
        t116_testalanlari.transfer_key as `key`,
        CASE WHEN t440_kaporta.T440_orjinal = 1 THEN 1 ELSE 0 END as orijinal,
        CASE WHEN t440_kaporta.T440_boya = 1 THEN 1 ELSE 0 END as boyali,
        CASE WHEN t440_kaporta.T440_degisen = 1 THEN 1 ELSE 0 END as degisen,
        CASE WHEN t440_kaporta.T440_duz = 1 THEN 1 ELSE 0 END as duz,
        t440_kaporta.T440_aciklama as note
    FROM t440_kaporta JOIN expertises ON expertises.uuid = t440_kaporta.T440_servis_UQ
    JOIN t400_srvbaslik ON t400_srvbaslik.T400_UQ = t440_kaporta.T440_servis_UQ
    JOIN t116_testalanlari ON t116_testalanlari.T116_kod = t440_kaporta.T440_alan_kodu
    WHERE expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, `key`, orijinal, boyali, degisen, duz, note
    FROM expertise_bodyworks WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def bodywork_notes(limit: int):
    """expertise_bodywork_notes tablosunu test et."""

    typer.echo("expertise_bodywork_notes tablosu test ediliyor...")

        # # Check total amount
    # source_table = connection.execute(text("SELECT SUM(T440_orjinal) FROM t440_kaporta"))
    # source_total = source_table.fetchone()
    #
    # target_table = connection.execute(text("SELECT SUM(orijinal) FROM expertise_bodyworks"))
    # target_total = target_table.fetchone()
    #
    # if source_total[0] != target_total[0]:
    #     message = typer.style(f"Toplam miktar farklı! Kaynak: {source_total[0]} - Hedef: {target_total[0]}", fg=typer.colors.RED, bold=True)
    #     typer.echo(message)
    # else:
    #     message = typer.style(f"Toplam miktar aynı! Kaynak: {source_total[0]} - Hedef: {target_total[0]}", fg=typer.colors.GREEN, bold=True)
    #     typer.echo(message)

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT expertises.id as expertise_id,
        t402_srvnotlari.T402_aciklama as note
    FROM t402_srvnotlari JOIN expertises ON expertises.uuid = t402_srvnotlari.T402_servis_UQ
    WHERE t402_srvnotlari.T402_test_kodu = 4 AND expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, note
    FROM expertise_bodywork_notes WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    # comparison = sql_source.compare(sql_target)
    # print(comparison)
    compare_tables(sql_source, sql_target, connection)


@app.command()
def brake(limit: int):
    """brake tablosunu test et."""

    typer.echo("brake tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        expertises.id as expertise_id,
        fren.T420_testtarihi as date,
        yanal_kayma.T410_onkayma as yanal_kayma_on,
        yanal_kayma.T410_arkakayma as yanal_kayma_arka,
        fren.T420_maksimumA as max_kuvvet_on_1,
        fren.T420_maksimumB as max_kuvvet_on_2,
        fren.T420_maksimumC as max_kuvvet_arka_1,
        fren.T420_maksimumD as max_kuvvet_arka_2,
        fren.T420_onfrendngszOran as dengesizlik_orani_on,
        fren.T420_arkafrendngszOran as dengesizlik_orani_arka,
        fren.T420_elfrenidngszOran as el_freni_dengesizlik_orani,
        fren.T420_yalpasurtunmeA as yalpa_orani_on_1,
        fren.T420_yalpasurtunmeB as yalpa_orani_on_2,
        fren.T420_yalpasurtunmeC as yalpa_orani_arka_1,
        fren.T420_yalpasurtunmeD as yalpa_orani_arka_2,
        suspansiyon.T430_OranA as suspansiyon_on_1,
        suspansiyon.T430_OranB as suspansiyon_on_2,
        suspansiyon.T430_OranC as suspansiyon_arka_1,
        suspansiyon.T430_OranD as suspansiyon_arka_2,
        'manuel' as type,
        NULL as on_dingil_bosta_a,
        NULL as on_dingil_bosta_b,
        NULL as arka_dingil_bosta_a,
        NULL as arka_dingil_bosta_b,
        NULL as el_freni_kuvvet_a,
        NULL as el_freni_kuvvet_b
    FROM t420_fren as fren
        JOIN expertises ON expertises.uuid = fren.T420_servis_uq
        LEFT JOIN t410_yanalkayma as yanal_kayma ON yanal_kayma.T410_servis_uq = fren.T420_servis_uq
        LEFT JOIN t430_suspansiyon as suspansiyon ON suspansiyon.T430_servis_UQ = fren.T420_servis_uq
    WHERE expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT
        expertise_id, date, yanal_kayma_on,
        yanal_kayma_arka,
        max_kuvvet_on_1,
        max_kuvvet_on_2,
        max_kuvvet_arka_1,
        max_kuvvet_arka_2,
        dengesizlik_orani_on,
        dengesizlik_orani_arka,
        el_freni_dengesizlik_orani,
        yalpa_orani_on_1,
        yalpa_orani_on_2,
        yalpa_orani_arka_1,
        yalpa_orani_arka_2,
        suspansiyon_on_1,
        suspansiyon_on_2,
        suspansiyon_arka_1,
        suspansiyon_arka_2,
        type,
        on_dingil_bosta_a,
        on_dingil_bosta_b,
        arka_dingil_bosta_a,
        arka_dingil_bosta_b,
        el_freni_kuvvet_a,
        el_freni_kuvvet_b
    FROM expertise_brakes WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def component_notes(limit: int):
    """component_notes tablosunu test et."""

    typer.echo("component_notes tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        expertises.id as expertise_id,
        t402_srvnotlari.T402_aciklama as note
    FROM t402_srvnotlari
    JOIN expertises ON expertises.uuid = t402_srvnotlari.T402_servis_UQ
    WHERE t402_srvnotlari.T402_test_kodu = 11 AND expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, note
    FROM expertise_component_notes WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def components(limit: int):
    """components tablosunu test et."""

    typer.echo("components tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        expertises.id as expertise_id,
        test_alan.transfer_key as `key`,
        CASE
            WHEN component.T451_sonuc_kodu = 1060 THEN 4
            WHEN component.T451_sonuc_kodu = 1030 THEN 1
            WHEN component.T451_sonuc_kodu = 1010 THEN 3
            WHEN component.T451_sonuc_kodu = 1120 THEN 5
            WHEN component.T451_sonuc_kodu = 1020 THEN 2
            ELSE 0
            END as status,
        t140_testsonuckod.value as answer,
        component.T451_aciklama as note
    FROM t451_mekaniktse as component
    JOIN expertises ON expertises.uuid = component.T451_servis_UQ
    JOIN t116_testalanlari as test_alan ON test_alan.T116_kod = component.T451_alan_kodu
    LEFT JOIN t140_testsonuckod ON t140_testsonuckod.T140_kod = component.T451_sonuc_kodu
    WHERE test_alan.T116_test_kodu = 8
    AND test_alan.T116_grupno IN (3,6)
    AND component.T451_alan_kodu IN (302,308,600,601,602,603)
    AND expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, `key`, status, answer, note
    FROM expertise_components WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def diagnostic_notes(limit: int):
    """diagnostic_notes tablosunu test et."""

    typer.echo("diagnostic_notes tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        expertises.id as expertise_id,
        t402_srvnotlari.T402_aciklama as note
    FROM t402_srvnotlari
            JOIN expertises ON expertises.uuid = t402_srvnotlari.T402_servis_UQ
    WHERE t402_srvnotlari.T402_test_kodu = 6 AND expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, note
    FROM expertise_diagnostic_notes WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def expertise_stock_campaigns(limit: int):
    """expertise_stock_campaigns tablosunu test et."""

    typer.echo("expertise_stock_campaigns tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        e.id as expertise_id,
        CASE
            WHEN srv.T400_fiyatgrubu = 1 THEN 0
            ELSE c.id
        END AS campaign_id
    FROM expertise_stocks AS es
    JOIN expertises AS e ON e.id = es.expertise_id
    LEFT JOIN t400_srvbaslik AS srv ON srv.T400_UQ = e.uuid
    LEFT JOIN campaigns AS c ON c.T122_kod = srv.T400_fiyatgrubu
    AND e.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, campaign_id
    FROM expertise_stocks WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def expertise_stocks(limit: int):
    """expertise_stocks tablosunu test et."""

    typer.echo("expertise_stocks tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT expertises.id as expertise_id,
        stocks.id as stock_id,
        0 as campaign_id,
        stocks.sorgu_hizmeti as sorgu_hizmeti,
        stocks.yol_yardimi as yol_yardimi,
        NULL as iskonto_amount,
        CASE
            WHEN t400_srvbaslik.T400_tltahsilattutari IS NOT NULL THEN t400_srvbaslik.T400_tltahsilattutari
            WHEN t400_srvbaslik.T400_kredi_tutar IS NOT NULL THEN t400_srvbaslik.T400_kredi_tutar
            ELSE t400_srvbaslik.T400_tlsatistutari
        END AS hizmet_tutari,
        t400_srvbaslik.T400_tllistefiyati as liste_fiyati,
        t400_srvbaslik.T400_belgetarihi as created_at,
        t400_srvbaslik.T400_belgetarihi as updated_at,
        NULL as deleted_at
    FROM t400_srvbaslik
        LEFT JOIN expertises ON expertises.uuid = t400_srvbaslik.T400_UQ
        LEFT JOIN stocks ON stocks.kod = t400_srvbaslik.T400_hizmet_UQ
    WHERE expertises.id IS NOT NULL AND expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, stock_id, campaign_id, sorgu_hizmeti, yol_yardimi, iskonto_amount,
    hizmet_tutari, liste_fiyati,created_at,updated_at,deleted_at
    FROM expertise_stocks WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def internal_control_notes(limit: int):
    """internal_control_notes tablosunu test et."""

    typer.echo("internal_control_notes tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        expertises.id as expertise_id,
        t402_srvnotlari.T402_aciklama as note
    FROM t402_srvnotlari
            JOIN expertises ON expertises.uuid = t402_srvnotlari.T402_servis_UQ
    WHERE t402_srvnotlari.T402_test_kodu = 8 AND expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, note
    FROM expertise_internal_controls_notes WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def internal_controls(limit: int):
    """internal_controls tablosunu test et."""

    typer.echo("internal_controls tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        expertises.id as expertise_id,
        t116_testalanlari.transfer_key as `key`,
        CASE
            WHEN t451_mekaniktse.T451_sonuc_kodu = 1050 THEN 1
            ELSE 0
            END as sorunlu_mu,
        t140_testsonuckod.value as answer,
        t451_mekaniktse.T451_aciklama as note
    FROM t451_mekaniktse
            INNER JOIN expertises ON expertises.uuid = t451_mekaniktse.T451_servis_UQ
            LEFT JOIN t140_testsonuckod ON t140_testsonuckod.T140_kod = t451_mekaniktse.T451_sonuc_kodu
            LEFT JOIN t116_testalanlari ON t116_testalanlari.t116_kod = t451_mekaniktse.T451_alan_kodu
    WHERE t451_mekaniktse.T451_alan_kodu BETWEEN 200 AND 210
    AND expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, `key`, sorunlu_mu, answer, note
    FROM expertise_internal_controls WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def iskonto(limit: int):
    """iskonto tablosunu test et."""

    typer.echo("iskonto tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        es.expertise_id,
        es.iskonto_amount
    FROM expertise_stocks AS es
    LEFT JOIN expertise_payments AS ep ON es.expertise_id = ep.expertise_id
    WHERE (ep.type = 'nakit' OR ep.type = 'kredi_karti')
    AND (es.liste_fiyati - ep.amount) > 0
    AND es.expertise_id > 500000
    AND es.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, iskonto_amount
    FROM expertise_stocks WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def old_expertises():
    """old_expertises tablosunu test et."""
    # @todo
    typer.echo("old_expertises tablosu test ediliyor...")

    check_ids_str = get_check_uuids()

    sql_source = text(f"""
    select
        expertises.t400_id as old_id,
        expertises.t400_uq as uuid,
        0 as user_id,
        kayitbranch.id as kayit_branch_id,
        branch.id as branch_id,
        satici.id as cari_id,
        satici.id as satici_id,
        alici.id as alici_id,
        car.id as car_id,
        expertises.t400_arackm as km,
        car.sase_no as sase_no,
        1 as km_type,
        expertises.t400_agirlik as net_agirlik,
        grup_deger.t122_aciklama as nereden_ulastiniz,
        expertises.t400_hasar_sorgulama as sorgu_hizmeti,
        expertises.t400_belgetarihi as belge_tarihi,
        expertises.t400_belgeno as belge_no,
        case when expertises.t400_belgeozelkodu in (0, -1) then 0 else 1 end as belge_ozel_kodu,
        expertises.t400_sigorta_teklifi as sigorta_teklif_ver,
        expertises.t400_sitedeyayinyasak as yayin_yasagi,
        expertises.t400_kayit_yeri as kayit_yeri,
        1 as status,
        null as odeme_turu,
        null as bodywork_image,
        null as diagnostic_file,
        null as arac_kontrol_user,
        case when stock.car = 1 then 1 else 0 end as arac_kontrol,
        null as fren_kontrol_user,
        case when stock.brake = 1 then 1 else 0 end as fren_kontrol,
        null as kaporta_kontrol_user,
        case when stock.bodywork = 1 then 1 else 0 end as kaporta_kontrol,
        null as diagnostic_kontrol_user,
        case when stock.diagnostic = 1 then 1 else 0 end as diagnostic_kontrol,
        null as ic_kontrol_user,
        case when stock.internal_control = 1 then 1 else 0 end as ic_kontrol,
        null as lastik_jant_kontrol_user,
        case when stock.tire_and_rim = 1 then 1 else 0 end as lastik_jant_kontrol,
        null as alt_motor_kontrol_user,
        case when stock.sub_control_and_engine = 1 then 1 else 0 end as alt_motor_kontrol,
        null as komponent_kontrol_user,
        case when stock.component = 1 then 1 else 0 end as komponent_kontrol,
        null as co2_kontrol_user,
        case when stock.co2 = 1 then 1 else 0 end as co2_kontrol,
        null as hasar_sorgu_sonuc,
        null as kilometre_sorgu_sonuc,
        null as borc_sorgu_sonuc,
        null as ruhsat_sorgu_sonuc,
        case
            when expertises.t400_tahsilatbelgetipi = 431 then 'plus_kart'
            when expertises.t400_tahsilatbelgetipi is not null then 'normal'
            else null
            end as payment_type,
        case
            when t404_kupontakip.t404_kayittipi = 1 then 'kredi'
            when t404_kupontakip.t404_kayittipi is not null then 'puan'
            else null
            end as plus_card_payment_type,
        plus_card.id as plus_kart_id,
        null as audio_url,
        expertises.t400_cikistarihi as cikis_tarihi,
        1 as manuel_save,
        1 as employee_downloaded,
        1 as ftp_ok,
        expertises.t400_cikistarihi as ftp_date,
        expertises.t400_belgetarihi as created_at,
        expertises.t400_belgetarihi as updated_at,
        null as deleted_at
    from t400_srvbaslik as expertises
            left join t404_kupontakip on t404_kupontakip.t404_bagli_tablo_uq = expertises.t400_uq
            left join t200_hesplan on t200_hesplan.t200_uq = expertises.t400_musteri_uq
            left join branches as kayitbranch on kayitbranch.belge_kod = expertises.t400_subekodu
            left join branches as branch on branch.belge_kod = expertises.t400_srvsubekodu
            left join customers as satici on satici.kod = expertises.t400_satici_uq
            left join customers as alici on alici.kod = expertises.t400_musteri_uq
            left join cars as car on car.uq = expertises.t400_arac_uq
            left join stocks as stock on stock.kod = expertises.t400_hizmet_uq
            left join plus_cards as plus_card on plus_card.system_id = t200_hesplan.t200_barkodu
            left join t122_grupdeger as grup_deger on grup_deger.t122_kod = expertises.t400_reklamulasimyeri
    where grup_deger.t122_grupno = 4
    and plus_card.deleted_at is null
    and expertises.t400_uq in ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT
        old_id, uuid, user_id, kayit_branch_id, branch_id, cari_id, satici_id, alici_id, car_id, km, sase_no, km_type, net_agirlik, nereden_ulastiniz,
        sorgu_hizmeti, belge_tarihi, belge_no, belge_ozel_kodu, sigorta_teklif_ver, yayin_yasagi, kayit_yeri, status, odeme_turu, bodywork_image,
        diagnostic_file, arac_kontrol_user, arac_kontrol, fren_kontrol_user, fren_kontrol, kaporta_kontrol_user, kaporta_kontrol,
        diagnostic_kontrol_user, diagnostic_kontrol, ic_kontrol_user, ic_kontrol, lastik_jant_kontrol_user, lastik_jant_kontrol,
        alt_motor_kontrol_user, alt_motor_kontrol, komponent_kontrol_user, komponent_kontrol, co2_kontrol_user, co2_kontrol, hasar_sorgu_sonuc,
        kilometre_sorgu_sonuc, borc_sorgu_sonuc, ruhsat_sorgu_sonuc, payment_type, plus_card_payment_type, plus_kart_id, audio_url,
        cikis_tarihi, manuel_save, employee_downloaded, ftp_ok, ftp_date, created_at, updated_at, deleted_at
    FROM expertises WHERE uuid IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def plus_card_payments_last(limit: int):
    """plus_card_payments_last tablosunu test et."""

    typer.echo("plus_card_payments_last tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        expertises.id AS expertise_id,
        0 AS case_id,
        t404_kupontakip.T404_fiyat AS amount,
        'plus_kart' AS type,
        min_plus_cards.id AS plus_card_id,
        t400_srvbaslik.T400_belgetarihi AS created_at,
        t400_srvbaslik.T400_belgetarihi AS updated_at,
        2 AS update_data
    FROM t400_srvbaslik
    INNER JOIN expertises ON expertises.uuid = t400_srvbaslik.T400_UQ
    LEFT JOIN t404_kupontakip ON t404_kupontakip.T404_UQ = t400_srvbaslik.T400_harcanan_plus_UQ
    INNER JOIN (
        SELECT customer_id, MIN(id) AS id
        FROM plus_cards
        WHERE deleted_at IS NULL
        GROUP BY customer_id
    ) AS min_plus_cards ON min_plus_cards.customer_id = expertises.alici_id
    WHERE t400_srvbaslik.T400_tahsilatbelgetipi = 431
        AND expertises.deleted_at IS NULL
        AND expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, case_id, amount, type, plus_card_id, created_at, updated_at, update_data
    FROM expertise_payments WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def plus_card_payments(limit: int):
    """plus_card_payments tablosunu test et."""

    typer.echo("plus_card_payments tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        expertises.id as expertise_id,
        0 as case_id,
        t404_kupontakip.T404_fiyat as amount,
        'plus_kart' as type,
        plus_cards.id as plus_card_id,
        t400_srvbaslik.T400_belgetarihi as created_at,
        t400_srvbaslik.T400_belgetarihi as updated_at,
        2 as update_data
    FROM t400_srvbaslik
    INNER JOIN expertises On expertises.uuid = t400_srvbaslik.T400_UQ
    INNER JOIN plus_cards ON plus_cards.customer_id = expertises.alici_id
    Left JOIN t404_kupontakip on t404_kupontakip.T404_UQ = t400_srvbaslik.T400_harcanan_plus_UQ
    WHERE t400_srvbaslik.T400_tahsilatbelgetipi = 431
    AND expertises.deleted_at IS NULL
    AND plus_cards.deleted_at IS NULL
    AND expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, case_id, amount, type, plus_card_id, created_at, updated_at, update_data
    FROM expertise_payments WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def query_logs_kalanlar_update():
    """query_logs_kalanlar_update tablosunu test et."""

    typer.echo("query_logs_kalanlar_update tablosu test ediliyor...")
    # @todo
    check_ids_str = get_check_uuids()

    sql_source = text(f"""
    SELECT
    (SELECT T405_SORGUBASLIK.T405_json
     FROM T405_SORGUBASLIK
     WHERE T405_SORGUBASLIK.T405_servis_UQ = ql.uuid
       AND T405_SORGUBASLIK.T405_islem_tipi = 1
       AND T405_SORGUBASLIK.T405_aktif = 1
       AND T405_SORGUBASLIK.T405_tarih IS NOT NULL
       AND T405_SORGUBASLIK.T405_tutar IS NOT NULL
     LIMIT 1) AS hasar,
    (SELECT T405_SORGUBASLIK.T405_tutar
     FROM T405_SORGUBASLIK
     WHERE T405_SORGUBASLIK.T405_servis_UQ = ql.uuid
       AND T405_SORGUBASLIK.T405_islem_tipi = 1
       AND T405_SORGUBASLIK.T405_aktif = 1
       AND T405_SORGUBASLIK.T405_tarih IS NOT NULL
       AND T405_SORGUBASLIK.T405_tutar IS NOT NULL
     LIMIT 1) AS hasar_ucret,
    (SELECT T405_SORGUBASLIK.T405_json
     FROM T405_SORGUBASLIK
     WHERE T405_SORGUBASLIK.T405_servis_UQ = ql.uuid
       AND T405_SORGUBASLIK.T405_islem_tipi = 2
       AND T405_SORGUBASLIK.T405_aktif = 1
       AND T405_SORGUBASLIK.T405_tarih IS NOT NULL
       AND T405_SORGUBASLIK.T405_tutar IS NOT NULL
     LIMIT 1) AS kilometre,
    (SELECT T405_SORGUBASLIK.T405_tutar
     FROM T405_SORGUBASLIK
     WHERE T405_SORGUBASLIK.T405_servis_UQ = ql.uuid
       AND T405_SORGUBASLIK.T405_islem_tipi = 2
       AND T405_SORGUBASLIK.T405_aktif = 1
       AND T405_SORGUBASLIK.T405_tarih IS NOT NULL
       AND T405_SORGUBASLIK.T405_tutar IS NOT NULL
     LIMIT 1) AS kilometre_ucret,
    (SELECT T405_SORGUBASLIK.T405_json
     FROM T405_SORGUBASLIK
     WHERE T405_SORGUBASLIK.T405_servis_UQ = ql.uuid
       AND T405_SORGUBASLIK.T405_islem_tipi = 3
       AND T405_SORGUBASLIK.T405_aktif = 1
       AND T405_SORGUBASLIK.T405_tarih IS NOT NULL
       AND T405_SORGUBASLIK.T405_tutar IS NOT NULL
     LIMIT 1) AS borc,
    (SELECT T405_SORGUBASLIK.T405_tutar
     FROM T405_SORGUBASLIK
     WHERE T405_SORGUBASLIK.T405_servis_UQ = ql.uuid
       AND T405_SORGUBASLIK.T405_islem_tipi = 3
       AND T405_SORGUBASLIK.T405_aktif = 1
       AND T405_SORGUBASLIK.T405_tarih IS NOT NULL
       AND T405_SORGUBASLIK.T405_tutar IS NOT NULL
     LIMIT 1) AS borc_ucret,
    (SELECT T405_SORGUBASLIK.T405_json
     FROM T405_SORGUBASLIK
     WHERE T405_SORGUBASLIK.T405_servis_UQ = ql.uuid
       AND T405_SORGUBASLIK.T405_islem_tipi = 4
       AND T405_SORGUBASLIK.T405_aktif = 1
       AND T405_SORGUBASLIK.T405_tarih IS NOT NULL
       AND T405_SORGUBASLIK.T405_tutar IS NOT NULL
     LIMIT 1) AS ruhsat,
    (SELECT T405_SORGUBASLIK.T405_tutar
     FROM T405_SORGUBASLIK
     WHERE T405_SORGUBASLIK.T405_servis_UQ = ql.uuid
       AND T405_SORGUBASLIK.T405_islem_tipi = 4
       AND T405_SORGUBASLIK.T405_aktif = 1
       AND T405_SORGUBASLIK.T405_tarih IS NOT NULL
       AND T405_SORGUBASLIK.T405_tutar IS NOT NULL
     LIMIT 1) AS ruhsat_ucret
    FROM query_logs AS ql
    WHERE ql.user_id IS NULL;
    AND t405_sorgubaslik.t405_servis_uq IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, hasar, hasar_ucret, kilometre, kilometre_ucret, borc, borc_ucret, ruhsat, ruhsat_ucret
    FROM query_logs WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def sub_control_and_engine_notes(limit: int):
    """sub_control_and_engine_notes tablosunu test et."""

    typer.echo("sub_control_and_engine_notes tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        expertises.id AS expertise_id,
        t402_srvnotlari.T402_aciklama AS note
    FROM t402_srvnotlari
        JOIN expertises ON expertises.uuid = t402_srvnotlari.T402_servis_UQ
    WHERE t402_srvnotlari.T402_test_kodu = 9
    AND expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, note
    FROM expertise_sub_control_and_engine_notes WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def sub_control_and_engines(limit: int):
    """sub_control_and_engines tablosunu test et."""

    typer.echo("sub_control_and_engines tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        expertises.id AS expertise_id,
        test_alan.transfer_key AS `key`,
        CASE
            WHEN mekanik.T451_sonuc_kodu = 1030 THEN 1
            WHEN mekanik.T451_sonuc_kodu = 1020 THEN 2
            WHEN mekanik.T451_sonuc_kodu = 1010 THEN 3
            WHEN mekanik.T451_sonuc_kodu = 1110 THEN 4
            WHEN mekanik.T451_sonuc_kodu = 1070 THEN 5
            WHEN mekanik.T451_sonuc_kodu = 1080 THEN 6
            WHEN mekanik.T451_sonuc_kodu = 1040 THEN 7
            WHEN mekanik.T451_sonuc_kodu = 1050 THEN 8
            ELSE 0
            END AS status,
        sonuc.value AS answer,
        mekanik.T451_aciklama AS note
    FROM t451_mekaniktse AS mekanik
    JOIN expertises ON expertises.uuid = mekanik.T451_servis_UQ
    LEFT JOIN t116_testalanlari AS test_alan ON test_alan.T116_kod = mekanik.T451_alan_kodu
    LEFT JOIN t140_testsonuckod AS sonuc ON sonuc.T140_kod = mekanik.T451_sonuc_kodu
    WHERE test_alan.T116_test_kodu = 8
    AND test_alan.T116_grupno IN (1, 3)
    AND test_alan.transfer_key IN (
        'motor_yag_kacak_kontrolu',
        'motor_su_kacak_kontrolu',
        'turbo_ve_intercooler_kacak_kontrolu',
        'motor_ufleme_kontrolu',
        'gozle_gorunur_kayislar',
        'aku_seviyesi_kontrolu_ve_tespitler',
        'motor_yag_seviye_kontrolu',
        'motor_sogutma_suyu_seviyesi',
        'antifiriz_derecesi',
        'co2_kacak_testi',
        'diferansiyel_yag_kacak_kontrolu',
        'sanziman_yag_kacak_kontrolu',
        'fren_sistemi_kontrolu',
        'direksiyon_sistemi_kontrolu',
        'on_duzen_ve_arka_duzen_kontrolu',
        'suspansiyon_sistemi_kontrolu',
        'egzoz_sistemi',
        'motor_muhafaza_ve_alt_bakalitler',
        'elektrik_sistemi'
    )
    AND expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, `key`, status, answer, note
    FROM expertise_sub_control_and_engines WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def query_logs_borc():
    """query_logs_borc tablosunu test et."""

    typer.echo("query_logs_borc tablosu test ediliyor...")

    check_ids_str = get_check_uuids()

    sql_source = text(f"""
    select
        null as user_id,
        dbtest_db.t405_sorgubaslik.t405_servis_uq as uuid,
        null as hasar,
        null as hasar_ucret,
        null as hasar_komisyon,
        null as kilometre,
        null as kilometre_ucret,
        null as kilometre_komisyon,
        dbtest_db.t405_sorgubaslik.t405_json as borc,
        dbtest_db.t405_sorgubaslik.t405_tutar as borc_ucret,
        null as borc_komisyon,
        null as degisen,
        null as degisen_ucret,
        null as degisen_komisyon,
        null as detail,
        null as detail_ucret,
        null as detail_komisyon,
        null as ruhsat,
        null as ruhsat_ucret,
        null as ruhsat_komisyon,
        dbtest_db.t405_sorgubaslik.t405_tarih as created_at,
        dbtest_db.t405_sorgubaslik.t405_tarih as updated_at
    FROM t405_sorgubaslik
    WHERE t405_sorgubaslik.t405_aktif = 1
    AND t405_sorgubaslik.t405_islem_tipi = 3
    AND t405_sorgubaslik.t405_json IS NOT NULL
    AND dbtest_db.t405_sorgubaslik.t405_servis_uq NOT IN (SELECT `t405_servis_uq` FROM t405_sorgubaslik WHERE `t405_islem_tipi` in (1,2,4) AND `t405_aktif` = 1)
    AND t405_sorgubaslik.t405_servis_uq IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT user_id, uuid, hasar, hasar_ucret, hasar_komisyon, kilometre ,kilometre_ucret ,kilometre_komisyon, `borc`,`borc_ucret`,`borc_komisyon`,`degisen`,`degisen_ucret`,`degisen_komisyon`,`detail`,`detail_ucret`,`detail_komisyon`,`ruhsat`,`ruhsat_ucret`,`ruhsat_komisyon`,`created_at`,`updated_at`
    FROM query_logs WHERE uuid IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def query_logs_hasar():
    """query_logs_hasar tablosunu test et."""
    # @todo
    typer.echo("query_logs_hasar tablosu test ediliyor...")

    check_ids_str = get_check_uuids()

    sql_source = text(f"""
    select
        null as user_id,
        dbtest_db.t405_sorgubaslik.t405_servis_uq as uuid,
        dbtest_db.t405_sorgubaslik.t405_json as hasar,
        dbtest_db.t405_sorgubaslik.t405_tutar as hasar_ucret,
        null as hasar_komisyon,
        null as kilometre,
        null as kilometre_ucret,
        null as kilometre_komisyon,
        null as borc,
        null as borc_ucret, null as borc_komisyon, null as degisen,
        null as degisen_ucret, null as degisen_komisyon, null as detail,
        null as detail_ucret, null as detail_komisyon, null as ruhsat,
        null as ruhsat_ucret, null as ruhsat_komisyon,
        dbtest_db.t405_sorgubaslik.t405_tarih as created_at,
        dbtest_db.t405_sorgubaslik.t405_tarih as updated_at
    from t405_sorgubaslik
    where t405_sorgubaslik.t405_aktif = 1
    and t405_sorgubaslik.t405_islem_tipi = 1
    and t405_sorgubaslik.t405_json is not nul
    and t405_sorgubaslik.t405_servis_uq in ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT user_id, uuid, hasar, hasar_ucret, hasar_komisyon, kilometre ,kilometre_ucret ,kilometre_komisyon, `borc`,`borc_ucret`,`borc_komisyon`,`degisen`,`degisen_ucret`,`degisen_komisyon`,`detail`,`detail_ucret`,`detail_komisyon`,`ruhsat`,`ruhsat_ucret`,`ruhsat_komisyon`,`created_at`,`updated_at`
    FROM query_logs WHERE uuid IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def query_logs_kilometre():
    """query_logs_kilometre tablosunu test et."""
    # @todo
    typer.echo("query_logs_kilometre tablosu test ediliyor...")

    check_ids_str = get_check_uuids()

    sql_source = text(f"""
    select
        null as user_id,
        dbtest_db.t405_sorgubaslik.t405_servis_uq as uuid,
        null as hasar,
        null as hasar_ucret,
        null as hasar_komisyon,
        dbtest_db.t405_sorgubaslik.t405_json as kilometre,
        dbtest_db.t405_sorgubaslik.t405_tutar as kilometre_ucret,
        null as kilometre_komisyon,
        null as borc,
        null as borc_ucret, null as borc_komisyon, null as degisen,
        null as degisen_ucret, null as degisen_komisyon, null as detail,
        null as detail_ucret, null as detail_komisyon, null as ruhsat,
        null as ruhsat_ucret, null as ruhsat_komisyon,
        dbtest_db.t405_sorgubaslik.t405_tarih as created_at,
        dbtest_db.t405_sorgubaslik.t405_tarih as updated_at
    from t405_sorgubaslik
    where t405_sorgubaslik.t405_aktif = 1
    and t405_sorgubaslik.t405_islem_tipi = 2
    and t405_sorgubaslik.t405_json is not null
    and dbtest_db.t405_sorgubaslik.t405_servis_uq not in (select `t405_servis_uq` from t405_sorgubaslik where `t405_islem_tipi` = 1 and `t405_aktif` = 1)
    and t405_sorgubaslik.t405_servis_uq in ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT user_id, uuid, hasar, hasar_ucret, hasar_komisyon, kilometre ,kilometre_ucret ,kilometre_komisyon, `borc`,`borc_ucret`,`borc_komisyon`,`degisen`,`degisen_ucret`,`degisen_komisyon`,`detail`,`detail_ucret`,`detail_komisyon`,`ruhsat`,`ruhsat_ucret`,`ruhsat_komisyon`,`created_at`,`updated_at`
    FROM query_logs WHERE uuid IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def query_logs_ruhsat():
    """query_logs_ruhsat tablosunu test et."""
    # @todo
    typer.echo("query_logs_ruhsat tablosu test ediliyor...")

    check_ids_str = get_check_uuids()

    sql_source = text(f"""
    select
        null as user_id,
        dbtest_db.t405_sorgubaslik.t405_servis_uq as uuid,
        null as hasar,
        null as hasar_ucret,
        null as hasar_komisyon,
        null as kilometre,
        null as kilometre_ucret,
        null as kilometre_komisyon,
        null as borc,
        null as borc_ucret,
        null as borc_komisyon,
        null as degisen,
        null as degisen_ucret,
        null as degisen_komisyon,
        null as detail,
        null as detail_ucret,
        null as detail_komisyon,
        dbtest_db.t405_sorgubaslik.t405_json as ruhsat,
        dbtest_db.t405_sorgubaslik.t405_tutar as ruhsat_ucret,
        null as ruhsat_komisyon,
        dbtest_db.t405_sorgubaslik.t405_tarih as created_at,
        dbtest_db.t405_sorgubaslik.t405_tarih as updated_at
    from t405_sorgubaslik
    where t405_sorgubaslik.t405_aktif = 1
    and t405_sorgubaslik.t405_islem_tipi = 4
    and t405_sorgubaslik.t405_json is not null
    and dbtest_db.t405_sorgubaslik.t405_servis_uq not in (select `t405_servis_uq` from t405_sorgubaslik where `t405_islem_tipi` in (1,2,3) and `t405_aktif` = 1)
    and t405_sorgubaslik.t405_servis_uq in ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT user_id, uuid, hasar, hasar_ucret, hasar_komisyon, kilometre ,kilometre_ucret ,kilometre_komisyon, `borc`,`borc_ucret`,`borc_komisyon`,`degisen`,`degisen_ucret`,`degisen_komisyon`,`detail`,`detail_ucret`,`detail_komisyon`,`ruhsat`,`ruhsat_ucret`,`ruhsat_komisyon`,`created_at`,`updated_at`
    FROM query_logs WHERE uuid IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def tire_and_rim_notes(limit: int):
    """tire_and_rim_notes tablosunu test et."""

    typer.echo("tire_and_rim_notes tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        expertises.id AS expertise_id,
        t402_srvnotlari.T402_aciklama AS note
    FROM t402_srvnotlari
            JOIN expertises ON expertises.uuid = t402_srvnotlari.T402_servis_UQ
    WHERE t402_srvnotlari.T402_test_kodu = 10
    AND expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, note
    FROM expertise_tire_and_rim_notes WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


@app.command()
def tire_and_rims(limit: int):
    """tire_and_rims tablosunu test et."""

    typer.echo("tire_and_rims tablosu test ediliyor...")

    check_ids_str = get_check_ids(limit)

    sql_source = text(f"""
    SELECT
        expertises.id AS expertise_id,
        t116_testalanlari.transfer_key AS `key`,
        CASE WHEN mekanik.T451_sonuc_kodu = 1050 THEN 1 ELSE 0 END AS sorunlu_mu,
        mekanik.T451_aciklama as note,
        mekanik.T451_deger_1 AS dis,
        mekanik.T451_deger_2 AS basinc
    FROM t451_mekaniktse AS mekanik
            LEFT JOIN t116_testalanlari ON t116_testalanlari.T116_kod = mekanik.T451_alan_kodu
            LEFT JOIN t140_testsonuckod ON t140_testsonuckod.T140_kod = mekanik.T451_sonuc_kodu
            JOIN expertises ON expertises.uuid = mekanik.T451_servis_UQ
    WHERE mekanik.T451_alan_kodu BETWEEN 400 AND 403
    AND t116_testalanlari.T116_grupno = 4
    AND expertises.id IN ({check_ids_str})""")
    # print(sql_source)
    # exit()

    sql_target = text(f"""SELECT expertise_id, `key`, sorunlu_mu,note, dis, basinc
    FROM expertise_tire_and_rims WHERE expertise_id IN ({check_ids_str})""")
    # print(sql_target)
    # exit()

    compare_tables(sql_source, sql_target, connection)


if __name__ == "__main__":
    app()
    close_db()


