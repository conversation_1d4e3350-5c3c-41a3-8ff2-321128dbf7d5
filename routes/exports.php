<?php

use App\Http\Controllers\Exports\Pdf\SingleCustomerController;
use Illuminate\Support\Facades\Route;


/*
|--------------------------------------------------------------------------
| Export Routes
|--------------------------------------------------------------------------
|
| Here is where you can register web routes for your application. These
| routes are loaded by the RouteServiceProvider and all of them will
| be assigned to the "web" middleware group. Make something great!
|
*/

Route::group(['prefix' => 'pdf', 'as' => 'pdf.'], function () {
    Route::get('single-customer/{customer}', SingleCustomerController::class)->name('single-customer');
});
