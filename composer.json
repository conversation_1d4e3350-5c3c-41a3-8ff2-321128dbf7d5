{"name": "laravel/laravel", "type": "project", "description": "The skeleton application for the Laravel framework.", "keywords": ["laravel", "framework"], "license": "MIT", "require": {"php": "^8.1", "ext-curl": "*", "ext-soap": "*", "google/apiclient": "^2.15", "guzzlehttp/guzzle": "^7.2", "iyzico/iyzipay-php": "^2.0", "laravel/framework": "^10.10", "laravel/horizon": "^5.21", "laravel/sanctum": "^3.3", "laravel/scout": "^10.8", "laravel/telescope": "^4.17", "laravel/tinker": "^2.8", "livewire/livewire": "^3.4", "maatwebsite/excel": "^3.1", "masbug/flysystem-google-drive-ext": "^2.3", "opcodesio/log-viewer": "^3.12", "predis/predis": "^2.2", "simplesoftwareio/simple-qrcode": "^4.2", "spatie/laravel-pdf": "^1.1", "spomky-labs/otphp": "^11.2", "teamtnt/laravel-scout-tntsearch-driver": "^13.2", "teamtnt/tntsearch": "^4.2", "yajra/laravel-datatables": "^10.1", "yaza/laravel-google-drive-storage": "^2.0"}, "require-dev": {"barryvdh/laravel-debugbar": "^3.9", "fakerphp/faker": "^1.9.1", "laravel/pint": "^1.0", "laravel/sail": "^1.18", "mockery/mockery": "^1.4.4", "nunomaduro/collision": "^7.0", "phpunit/phpunit": "^10.1", "spatie/laravel-ignition": "^2.0"}, "autoload": {"psr-4": {"App\\": "app/", "Database\\Factories\\": "database/factories/", "Database\\Seeders\\": "database/seeders/"}, "files": ["app/Helper/helper_functions.php"]}, "autoload-dev": {"psr-4": {"Tests\\": "tests/"}}, "scripts": {"post-autoload-dump": ["Illuminate\\Foundation\\ComposerScripts::postAutoloadDump", "@php artisan package:discover --ansi"], "post-update-cmd": ["@php artisan vendor:publish --tag=laravel-assets --ansi --force"], "post-root-package-install": ["@php -r \"file_exists('.env') || copy('.env.example', '.env');\""], "post-create-project-cmd": ["@php artisan key:generate --ansi"]}, "extra": {"laravel": {"dont-discover": []}}, "config": {"optimize-autoloader": true, "preferred-install": "dist", "sort-packages": true, "allow-plugins": {"pestphp/pest-plugin": true, "php-http/discovery": true}}, "minimum-stability": "stable", "prefer-stable": true}