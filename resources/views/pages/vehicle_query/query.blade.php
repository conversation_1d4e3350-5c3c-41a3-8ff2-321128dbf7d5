@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Sorgulama')
@section('content')
    <form id="mainForm" method="post" action="{{ route('updateExpertise') }}" enctype="multipart/form-data">@csrf
        <div class="row">
            <div class="col-md-12">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item" style="position: sticky;top: 1rem;z-index: 2">
                        <h2 class="accordion-header" id="belgeHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse" data-bs-target="#belge" aria-expanded="true" aria-controls="arama"> <PERSON><PERSON></button>
                        </h2>
                        <div id="belge" class="accordion-collapse collapse show" aria-labelledby="belgeHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" role="switch" id="hasar" name="hasar" value="hasar" checked="">
                                            <label class="form-check-label" for="hasar">Hasar</label>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" role="switch" id="kilometre" name="kilometre" value="kilometre" checked="">
                                            <label class="form-check-label" for="kilometre">Kilometre</label>
                                        </div>
                                    </div>
                                    <div class="col">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" role="switch" id="borc" name="borc" value="borc" checked="">
                                            <label class="form-check-label" for="borc">Borç Sorgusu</label>
                                        </div>
                                    </div>
                                    @if(Auth()->user()->type == "admin")
                                        <div class="col">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" role="switch" id="arac" name="AracDetay" value="AracDetay" checked="">
                                                <label class="form-check-label" for="arac">Araç Detayı</label>
                                            </div>
                                        </div>
                                        <div class="col">
                                            <div class="form-check form-switch">
                                                <input class="form-check-input" type="checkbox" role="switch" id="degisen" name="Degisen" value="Degisen" checked="">
                                                <label class="form-check-label" for="degisen">Değişen</label>
                                            </div>
                                        </div>
                                    @endif
                                    <div class="col">
                                        <div class="form-check form-switch">
                                            <input class="form-check-input" type="checkbox" role="switch" id="ruhsat" name="Ruhsat" value="Ruhsat" checked="">
                                            <label class="form-check-label" for="ruhsat">Ruhsat</label>
                                        </div>
                                    </div>
                                </div>
                                <div class="row">
                                    <div class="col-md-4">
                                        <label>Plaka</label>
                                        <input class="form-control form-control-sm" id="plaka" value="">
                                        <input type="hidden" name="tc" id="tc" value="">
                                        <input type="hidden" id="expertiseUuid" value="">
                                    </div>
                                    <div class="col-md-5">
                                        <label>Şase</label>
                                        <input class="form-control form-control-sm" id="sasi" value="">
                                    </div>
                                    <div class="col-md-2 pt-3">
                                        <button type="button" id="searchBtn" class="btn btn-primary">Ara</button>
                                        <button type="button" id="startQuery" class="btn btn-primary" disabled>Sorgula</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="kaportaSonuclariHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#kaportaSonuclari" aria-expanded="true"
                                    aria-controls="kaportaSonuclari">
                                Sonuçlar
                            </button>
                        </h2>
                        <div id="kaportaSonuclari" class="accordion-collapse collapse show" aria-labelledby="kaportaSonuclariHeading">
                            <div class="accordion-body" style="overflow: auto;max-width: 100%;">
                                <div class="row">
                                    <div class="col-4">
                                        <div class="card">
                                            <div class="card-header"><h5 class="card-title">Hasar Sonuçları</h5></div>
                                            <div class="card-body" id="hasarSonucu">
                                                @if($expertise["queryLogs"] != null)
                                                    @if($expertise["queryLogs"]->hasar != null)
                                                        @php
                                                            $hasar = json_decode($expertise["queryLogs"]->hasar);
                                                        @endphp
                                                        <p class="text-center">{{ $hasar->data->result }}</p>
                                                        <img src="{{ $hasar->data->imageurl }}" width="100%">
                                                    @endif
                                                @endif
                                            </div>
                                        </div>
                                        @if(Auth()->user()->type == "admin")
                                            <div class="card">
                                                <div class="card-header"><h5 class="card-title">Araç Bilgileri</h5></div>
                                                <div class="card-body" id="aracbilgileri">
                                                    @if($expertise["queryLogs"] != null)
                                                        @if($expertise["queryLogs"]->detail != null)
                                                            @php
                                                                $detail = json_decode($expertise["queryLogs"]->detail);
                                                            @endphp
                                                            <img src="{{ $detail->data->imageurl }}" width="100%">
                                                        @endif
                                                    @endif
                                                </div>
                                            </div>
                                            <div class="card">
                                                <div class="card-header"><h5 class="card-title">Değişen</h5></div>
                                                <div class="card-body" id="degisenSonuc">
                                                    @if($expertise["queryLogs"] != null)
                                                        @if($expertise["queryLogs"]->degisen != null)
                                                            @php
                                                                $details = json_decode($expertise["queryLogs"]->degisen);
                                                            @endphp
                                                            @if(is_array($details))
                                                                @foreach($details as $detail)
                                                                    <img src="{{ $detail?->data?->imageurl }}" width="100%">
                                                                @endforeach
                                                            @else
                                                                <img src="{{ $details?->data?->imageurl }}" width="100%">
                                                            @endif
                                                        @endif
                                                    @endif
                                                </div>
                                            </div>
                                        @endif
                                    </div>
                                    <div class="col-4">
                                        <div class="card">
                                            <div class="card-header"><h5 class="card-title">Kilometre Sonuçları</h5></div>
                                            <div class="card-body" id="kilometreSonucu">
                                                @if($expertise["queryLogs"] != null)
                                                    @if($expertise["queryLogs"]->kilometre != null)
                                                        @php
                                                            $kilometre = json_decode($expertise["queryLogs"]->kilometre);
                                                        @endphp
                                                        @if($kilometre->data->imageurl)
                                                            <img src="{{ $kilometre->data->imageurl }}" width="100%">
                                                        @elseif($kilometre->data->kilometerQueryDetails)
                                                            <div class="table-responsive">
                                                                <table class="table table-striped">
                                                                    <thead>
                                                                    <tr>
                                                                        <th>KM Tarih</th>
                                                                        <th>KM</th>
                                                                        <th>Km Oynanmış mı?</th>
                                                                    </tr>
                                                                    </thead>
                                                                    <tbody>
                                                                    @foreach($kilometre->data->kilometerQueryDetails as $kmDetail)
                                                                        <tr>
                                                                            <td>{{ \Carbon\Carbon::parse($kmDetail->kmTarih)->translatedFormat('d F Y') }}</td>
                                                                            <td>{{ $kmDetail->km }}</td>
                                                                            <td>{{ $kmDetail->isThereAProblem ? "Evet" : "Hayır" }}</td>
                                                                        </tr>
                                                                    @endforeach
                                                                    </tbody>
                                                                </table>
                                                            </div>
                                                        @endif
                                                    @endif
                                                @endif
                                            </div>
                                        </div>
                                        <div class="card">
                                            <div class="card-header"><h5 class="card-title">Ruhsat</h5></div>
                                            <div class="card-body" id="ruhsatSonuc">
                                                @if($expertise["queryLogs"] != null)
                                                    @if($expertise["queryLogs"]->ruhsat != null)
                                                        @php
                                                            $ruhsat = json_decode($expertise["queryLogs"]->ruhsat);
                                                        @endphp
                                                        <table class="table">
                                                            <tr><th>Seri Numarası</th><td>{{ $ruhsat->data->serialnumber }}</td></tr>
                                                            <tr><th>Plaka</th><td>{{ $ruhsat->data->plate }}</td></tr>
                                                            <tr><th>Şasi</th><td>{{ $ruhsat->data->chassis }}</td></tr>
                                                            <tr><th>Motor Numarası</th><td>{{ $ruhsat->data->enginenumber }}</td></tr>
                                                            <tr><th>Model Yılı</th><td>{{ $ruhsat->data->modelyear }}</td></tr>
                                                            <tr><th>Marka</th><td>{{ $ruhsat->data->brand }}</td></tr>
                                                            <tr><th>Model</th><td>{{ $ruhsat->data->modelname }}</td></tr>
                                                            <tr><th>Yakıt Tipi</th><td>{{ $ruhsat->data->fueltype }}</td></tr>
                                                            <tr><th>Kayıt Tarihi</th><td>{{ $ruhsat->data->registerdate }}</td></tr>
                                                            <tr><th>Kayıt Başlangıç Tarihi</th><td>{{ $ruhsat->data->registerdateFrom }}</td></tr>
                                                            <tr><th>Yolcu Sayısı</th><td>{{ $ruhsat->data->passengercount }}</td></tr>
                                                            <tr><th>Kimlik Numarası</th><td>{{ $ruhsat->data->idnumber }}</td></tr>
                                                            <tr><th>Adı Soyadı </th><td>{{ $ruhsat->data->surname }}</td></tr>
                                                            <tr><th>Renk </th><td>{{ $ruhsat->data->color }}</td></tr>
                                                            <tr><th>Motor Kapasitesi </th><td>{{ $ruhsat->data->enginecapacity }}</td></tr>
                                                            <tr><th>Motor Gücü </th><td>{{ $ruhsat->data->enginepower }}</td></tr>
                                                            <tr><th>Sakınca Durumu </th><td>{{ $ruhsat->data->sakincadurumu }}</td></tr>
                                                            <tr><th>Kasko Değeri </th><td>{{ $ruhsat->data->kaskodegeri }}</td></tr>
                                                        </table>
                                                    @endif
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-4">
                                        <div class="card">
                                            <div class="card-header"><h5 class="card-title">Borç Sonuçları</h5></div>
                                            <div class="card-body" id="borcSonucu">
                                                @if($expertise["queryLogs"] != null)
                                                    @if($expertise["queryLogs"]->borc != null)
                                                        @php
                                                            $borc = json_decode($expertise["queryLogs"]->borc);
                                                            $totalBorc = 0;
                                                        @endphp
                                                        <table class="table">
                                                            <tbody>
                                                            <tr>
                                                                <th>Tip</th>
                                                                <th>Vade</th>
                                                                <th>Borç</th>
                                                                <th>G. Faizi</th>
                                                                <th>Toplam</th>
                                                            </tr>
                                                            @foreach($borc->data->vehicletax->data as $brc)
                                                                <tr>
                                                                    <td>{{ $brc->expirydate }}</td>
                                                                    <td>{{ $brc->installment }}</td>
                                                                    <td>{{ $brc->loanamount }}₺</td>
                                                                    <td>{{ $brc->delayamount }}₺</td>
                                                                    <td>{{ $brc->totalamount }}₺</td>
                                                                </tr>
                                                                @php $totalBorc += $brc->totalamount @endphp
                                                            @endforeach
                                                            <tr>
                                                                <td>Avrupa Otoyolu Cezaları</td>
                                                                <td>1</td>
                                                                <td>{{ isset($borc->data->roadfines->data[0]) ? $borc->data->roadfines->data[0]->avrupa : '-' }}₺</td>
                                                                <td>0 ₺</td>
                                                                <td>{{ isset($borc->data->roadfines->data[0]) ? $borc->data->roadfines->data[0]->avrupa : '-' }}₺</td>
                                                                @php if(!empty($borc->data->roadfines->data[0]->avrupa)){ $totalBorc += $borc->data->roadfines->data[0]->avrupa; } @endphp
                                                            </tr>
                                                            <tr>
                                                                <td>HGS / OGS Cezaları</td>
                                                                <td>1</td>
                                                                <td>{{ isset($borc->data->roadfines->data[0]) ? $borc->data->roadfines->data[0]->hgsogs : '-' }}₺</td>
                                                                <td>0 ₺</td>
                                                                <td>{{ isset($borc->data->roadfines->data[0]) ? $borc->data->roadfines->data[0]->hgsogs : '-' }}₺</td>
                                                                @php if(!empty($borc->data->roadfines->data[0]->hgsogs)){ $totalBorc += $borc->data->roadfines->data[0]->hgsogs; } @endphp
                                                            </tr>
                                                            <tr>
                                                                <td>Anadolu Otoyolu Cezaları</td>
                                                                <td>1</td>
                                                                <td>{{ isset($borc->data->roadfines->data[0]) ? $borc->data->roadfines->data[0]->anadolu : '-' }}₺</td>
                                                                <td>0 ₺</td>
                                                                <td>{{ isset($borc->data->roadfines->data[0]) ? $borc->data->roadfines->data[0]->anadolu : '-' }}₺</td>
                                                                @php if(!empty($borc->data->roadfines->data[0]->anadolu)){ $totalBorc += $borc->data->roadfines->data[0]->anadolu; } @endphp
                                                            </tr>
                                                            <tr>
                                                                <td>Avrasya Tüneli Cezaları</td>
                                                                <td>1</td>
                                                                <td>{{ isset($borc->data->roadfines->data[0]) ? $borc->data->roadfines->data[0]->avrasya : '-' }}₺</td>
                                                                <td>0 ₺</td>
                                                                <td>{{ isset($borc->data->roadfines->data[0]) ? $borc->data->roadfines->data[0]->avrasya : '-' }}₺</td>
                                                                @php if(!empty($borc->data->roadfines->data[0]->avrasya)) { $totalBorc += $borc->data->roadfines->data[0]->avrasya; } @endphp
                                                            </tr>
                                                            <tr>
                                                                <td>Kuzey Çevre Otoyolu + Yavuz Sultan Selim Köprü Geçiş Cezaları</td>
                                                                <td>1</td>
                                                                <td>{{ isset($borc->data->roadfines->data[0]) ? $borc->data->roadfines->data[0]->yssKuzeyCevre : '-' }}₺</td>
                                                                <td>0 ₺</td>
                                                                <td>{{ isset($borc->data->roadfines->data[0]) ? $borc->data->roadfines->data[0]->yssKuzeyCevre : '-' }}₺</td>
                                                                @php if(!empty($borc->data->roadfines->data[0]->yssKuzeyCevre)){ $totalBorc += $borc->data->roadfines->data[0]->yssKuzeyCevre; } @endphp
                                                            </tr>
                                                            <tr>
                                                                <td>Gebze-İzmir Otoyolu + Osmangazi Köprü Geçiş Cezaları</td>
                                                                <td>1</td>
                                                                <td>{{ isset($borc->data->roadfines->data[0]) ? $borc->data->roadfines->data[0]->gebzeOrhangaziIzmir : '-' }}₺</td>
                                                                <td>0 ₺</td>
                                                                <td>{{ isset($borc->data->roadfines->data[0]) ? $borc->data->roadfines->data[0]->gebzeOrhangaziIzmir : '-' }}₺</td>
                                                                @php if(!empty($borc->data->roadfines->data[0]->gebzeOrhangaziIzmir)){ $totalBorc += $borc->data->roadfines->data[0]->gebzeOrhangaziIzmir; } @endphp
                                                            </tr>
                                                            <tr>
                                                                <td colspan="4" class="text-right">Genel Toplam:</td>
                                                                <td>{{ $totalBorc }}₺</td>
                                                            </tr>
                                                            </tbody>
                                                        </table>
                                                    @endif
                                                @endif
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </form>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <!-- Select2 Cdn -->
    <script src="/assets/js/select2.min.js"></script>
    <script>
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }

        (function (){
            "use strict"
            let hasarSonucu = "";
            let kiometreSonucu = "";
            let borcSonucu = "";

            $(document).on("click", "#searchBtn", function () {
                $(this).attr('disabled', 'true').text("Aranıyor...");

                var plaka = $("#plaka").val();

                if (validateInput("Plaka", plaka)) {
                    $.ajax({
                        url: '{{ route("vehicleQueries.search") }}',
                        type: "POST",
                        dataType: "json",
                        data: {
                            _token: '{{ csrf_token() }}',
                            plaka: plaka
                        },
                        success: function (response) {
                            if (response.success == true) {
                                $('#tc').val(response.kimlik);
                                $('#expertiseUuid').val(response.uuid);
                                $("#sasi").val(response.chasis_no);

                                Toast.fire({
                                    icon: 'success',
                                    title: 'Plaka sonucu alındı. Sorgulama yapabilirsiniz.'
                                })

                                $('#searchBtn').removeAttr('disabled').text("Ara");
                            } else {
                                Toast.fire({
                                    icon: 'error',
                                    title: Object.values(response.errors)[0]
                                })
                            }

                            $('#startQuery').removeAttr('disabled');
                        },
                        error: function (xhr) {
                            if (xhr.responseJSON) {
                                Toast.fire({
                                    icon: 'error',
                                    title: Object.values(xhr.responseJSON.errors)[0]
                                })
                            } else {
                                Toast.fire({
                                    icon: 'error',
                                    title: xhr.responseText
                                })
                            }

                            $('#searchBtn').removeAttr('disabled').text("Ara");
                        }
                    })
                }
            });

            function validateInput(label, value) {
                if (!value || value.trim() === "") {
                    Toast.fire({
                        icon: "error",
                        title: `${label} Zorunludur!`
                    });
                    return false;
                }
                return true;
            }

            function checkComplete() {
                completedQueries++;
                if (completedQueries === totalQueries) {
                    $("#startQuery").removeAttr('disabled').text("Sorgula");
                }
            }

            $(document).on("click", "#startQuery", function () {
                $(this).attr('disabled', 'true').text("Sorgulanıyor...");

                var plaka = $("#plaka").val();
                var sasi = $("#sasi").val();
                var tc = $("#tc").val();
                var type = $('input[name="type"]:checked').val();

                let delay = 0; // Başlangıç gecikmesi
                let totalQueries = $('input[type="checkbox"]:checked').not('[value="Degisen"]').length;
                let completedQueries = 0; // Tamamlanan sorgu sayısı

                $('input[type="checkbox"]:checked').each(function () {
                    delay += 3000; // Her işlem için 3 saniye ekle
                    let queryType = $(this).val();

                    setTimeout(() => {
                        if (queryType === "hasar" && validateInput("Şase No", sasi)) {
                            hasar('Sasi', sasi).then(checkComplete);
                        } else if (queryType === "kilometre" && validateInput("Şase No", sasi)) {
                            kilometre('Sasi', sasi).then(checkComplete);
                        } else if (queryType === "borc" && validateInput("T.C. Kimlik No", tc) && validateInput("Plaka", plaka)) {
                            borc('Plaka', plaka, tc).then(checkComplete);
                        } else if (queryType === "AracDetay" && validateInput("Şase No", sasi)) {
                            detay('Sasi', sasi).then(checkComplete);
                        } else if (queryType === "Ruhsat" && validateInput("Şase No", sasi)) {
                            ruhsat('Sasi', sasi).then(checkComplete);
                        } else {
                            checkComplete(); // Geçersiz sorgu veya eksik veri
                        }
                    }, delay);
                });
            });

            async function hasar(type,arac) {
                var sasi = $("#sasi").val();
                var tc = $("#tc").val();
                var plaka = $("#plaka").val();
                var expertiseUuid = $("#expertiseUuid").val();

                $("#hasarSonucu").html('<center><span class="loader"></span></center>');
                if(type == "Sasi"){
                    var url = "{{ url($expertise['hasarSaseSorguUrl']) }}/" + expertiseUuid +  "/"+arac+"/hasar";
                }else{
                    var url = "{{ url($expertise['hasarPlakaSorguUrl']) }}/" + expertiseUuid +  "/"+arac+"/hasar";
                }
                $.get(url).then(res => {
                    var html = "";
                    if(res){
                        var dataListesi = res.damageDateList.data;
                        var datatoplam = res.damageDateList.total;
                        html += '<p><strong>Rapor Sonucu :</strong>'+res.result+'</p>';
                        if(datatoplam > 0){
                            html += '<p><strong>Hasar Kayıtları :</strong>'+datatoplam+'</p>';
                            dataListesi.forEach(function(ogeleman) {
                                if(ogeleman.changingparts){
                                    html += '<p>'+ogeleman.date+' - Değişen Var</p>';
                                    if($('input[name="Degisen"]')[0].checked == true){
                                        if(type == "Sasi"){
                                            degisen(type,sasi,ogeleman.date,ogeleman.damageDateKeyValue);
                                        }else{
                                            degisen(type,plaka,ogeleman.date,ogeleman.damageDateKeyValue)
                                        }
                                    }
                                }else{
                                    html += '<p>'+ogeleman.date+' - Değişen Yok</p>';
                                }
                            });
                        }
                        html += '<a href="'+res.imageurl+'" target="_blank"><img src="'+res.imageurl+'" width="100%"></a>';
                    }else{
                        html += '<p><strong>Sonuç Alınamadı </strong></p>';
                        let responseObj = typeof res === 'string' ? JSON.parse(res) : res;
                        if (responseObj.success === false){
                            html += '<p><strong> '+responseObj.message+' </strong></p>';
                        }
                    }
                    $("#hasarSonucu").html(html);
                    var url = $("#export").attr("data-url");
                    $("#export").attr("href",url);
                });

            }

            async function kilometre(type, arac) {
                var expertiseUuid = $("#expertiseUuid").val();
                $("#kilometreSonucu").html('<center><span class="loader"></span></center>');
                if(type == "Sasi"){
                    var url = "{{ url($expertise['kilometreSaseSorguUrl']) }}/" + expertiseUuid +  "/"+arac+"/kilometre";
                }else{
                    var url = "{{ url($expertise['kilometrePlakaSorguUrl']) }}/" + expertiseUuid +  "/"+arac+"/kilometre";
                }
                $.get(url).then(res => {
                    kiometreSonucu = res;
                    var html = "";
                    if(res.imageurl){
                        html += '<a href="'+res.imageurl+'" target="_blank"><img src="'+res.imageurl+'" width="100%"></a>';
                    }else if(res.kilometerQueryDetails){
                        html += "<div class='table-responsive'>";
                        let rows = '';
                        res.kilometerQueryDetails.forEach(item => {
                            let tarih = new Date(item.kmTarih).toLocaleDateString('tr-TR', {
                                day: '2-digit',
                                month: 'long',
                                year: 'numeric'
                            });
                            rows += `
                                    <tr>
                                      <td>${tarih}</td>
                                      <td>${item.km}</td>
                                      <td>${item.isThereAProblem ? "Evet" : "Hayır"}</td>
                                    </tr>
                                  `;
                        });

                        html += `
                                  <table class="table table-striped">
                                    <thead>
                                      <tr>
                                        <th>KM Tarih</th>
                                        <th>KM</th>
                                        <th>Km Oynanmış mı?</th>
                                      </tr>
                                    </thead>
                                    <tbody>
                                      ${rows}
                                    </tbody>
                                  </table>
                                `;
                        html += "</div>";
                    }else{
                        html += '<p><strong>Sonuç Alınamadı </strong></p>';
                        let responseObj = typeof res === 'string' ? JSON.parse(res) : res;
                        if (responseObj.success === false){
                            html += '<p><strong> '+responseObj.message+' </strong></p>';
                        }
                    }
                    $("#kilometreSonucu").html(html);
                    var url = $("#export").attr("data-url");
                    $("#export").attr("href",url);
                });
            }

            async function borc(type, arac,tc) {
                var expertiseUuid = $("#expertiseUuid").val();
                $("#borcSonucu").html('<center><span class="loader"></span></center>');
                if(type == "Sasi"){
                    var url = "{{ url($expertise['borcSaseSorguUrl']) }}/" + expertiseUuid +  "/"+arac+"/borc/"+tc;
                }else{
                    var url = "{{ url($expertise['borcPlakaSorguUrl']) }}/" + expertiseUuid +  "/"+arac+"/borc/"+tc;
                }
                $.get(url).then(res => {
                    let total = 0;
                    borcSonucu = res;
                    var html = "";
                    if(res.vehicleTaxState < 2){
                        var dataListesi = res.vehicletax.data;
                        var dataToplam = res.vehicletax.total;
                        html += '<table class="table"><tr><th>Tip</th><th>Vade</th><th>Borç</th><th>G. Faizi</th><th>Toplam</th></tr>';
                        if(dataToplam > 0){
                            dataListesi.forEach(function(ogeleman) {
                                total += ogeleman.totalamount;
                                html += '<tr><td>'+ogeleman.period+'</td><td>'+ogeleman.installment+'</td><td>'+formatCurrency(ogeleman.loanamount)+'₺</td><td>'+formatCurrency(ogeleman.delayamount)+'₺</td><td>'+formatCurrency(ogeleman.totalamount)+'₺</td>';
                            });
                        }

                        var dataListesi2 = res.roadfines.data;
                        var dataToplam2 = res.roadfines.total;
                        if(dataToplam2 > 0){
                            html += '<tr><td>HGS/OGS</td><td>-</td><td>'+formatCurrency(dataListesi2[0].hgsogs)+'₺</td><td>0 ₺</td><td>'+formatCurrency(dataListesi2[0].hgsogs)+'₺</td>';
                            total += dataListesi2[0].hgsogs;
                            html += '<tr><td>Avrasya Tüneli</td><td>-</td><td>'+formatCurrency(dataListesi2[0].avrasya)+'₺</td><td>0 ₺</td><td>'+formatCurrency(dataListesi2[0].avrasya)+'₺</td>';
                            total += dataListesi2[0].avrasya;
                            html += '<tr><td>Gebze-İzmir Otoyolu + Osmangazi Köprü Geçiş Cezaları </td><td>-</td><td>'+formatCurrency(dataListesi2[0].gebzeOrhangaziIzmir)+'₺</td><td>0 ₺</td><td>'+formatCurrency(dataListesi2[0].gebzeOrhangaziIzmir)+'₺</td>';
                            total += dataListesi2[0].gebzeOrhangaziIzmir;
                            html += '<tr><td>Kuzey Çevre Otoyolu + Yavuz Sultan Selim Köprü Geçiş Cezaları </td><td>-</td><td>'+formatCurrency(dataListesi2[0].yssKuzeyCevre)+'₺</td><td>0 ₺</td><td>'+formatCurrency(dataListesi2[0].yssKuzeyCevre)+'₺</td>';
                            total += dataListesi2[0].yssKuzeyCevre;
                            html += '<tr><td>Avrupa Otoyolu Cezaları </td><td>-</td><td>'+formatCurrency(dataListesi2[0].avrupa)+'₺</td><td>0 ₺</td><td>'+formatCurrency(dataListesi2[0].avrupa)+'₺</td>';
                            total += dataListesi2[0].avrupa;
                            html += '<tr><td>Anadolu Otoyolu Cezaları </td><td>-</td><td>'+formatCurrency(dataListesi2[0].anadolu)+'₺</td><td>0 ₺</td><td>'+formatCurrency(dataListesi2[0].anadolu)+'₺</td>';
                            total += dataListesi2[0].anadolu;
                        }
                        html += '<tr><td colspan="4" class="text-right">Genel Toplam:</td><td >'+formatCurrency(total)+'₺</td>';
                        html += '</table>';
                    }else{
                        html += '<p><strong>Sonuç Alınamadı </strong></p>';
                        let responseObj = typeof res === 'string' ? JSON.parse(res) : res;
                        if (responseObj.success === false){
                            html += '<p><strong> '+responseObj.message+' </strong></p>';
                        }
                    }
                    $("#borcSonucu").html(html);
                    var url = $("#export").attr("data-url");
                    $("#export").attr("href",url);
                });
            }

            function formatCurrency(number) {
                // Sayıyı Türk Lirası formatına çevirirken virgül ve noktaları düzenle
                let formattedNumber = number.toFixed(2).replace('.', ',').replace(/(\d)(?=(\d{3})+(?!\d))/g, '$1.');

                return formattedNumber;
            }

            async function detay(type, arac) {
                var expertiseUuid = $("#expertiseUuid").val();
                $("#aracbilgileri").html('<center><span class="loader"></span></center>');
                if(type == "Sasi"){
                    var url = "{{ url($expertise['detaySaseSorguUrl']) }}/" + expertiseUuid +  "/"+arac+"/AracDetay";
                }else{
                    var url = "{{ url($expertise['detayPlakaSorguUrl']) }}/" + expertiseUuid +  "/"+arac+"/AracDetay";
                }
                $.get(url).then(res => {
                    let html = "";
                    html += "<img src='"+res.imageurl+"' width='100%'>";
                    let responseObj = typeof res === 'string' ? JSON.parse(res) : res;
                    if (responseObj.success === false){
                        html += '<p><strong> '+responseObj.message+' </strong></p>';
                    }
                    $("#aracbilgileri").html(html);
                    var url = $("#export").attr("data-url");
                    $("#export").attr("href",url);
                });
            }

            async function degisen(type, arac,date,key = '') {
                var expertiseUuid = $("#expertiseUuid").val();
                $("#degisenSonuc").html('<center><span class="loader"></span></center>');
                if(type == "Sasi"){
                    var url = "{{ url($expertise['degisenSaseSorguUrl']) }}/" + expertiseUuid +  "/"+arac+"/Degisen/0/"+date+'/'+key;
                }else{
                    var url = "{{ url($expertise['degisenPlakaSorguUrl']) }}/" + expertiseUuid +  "/"+arac+"/Degisen/0/"+date+'/'+key;
                }
                $.get(url).then(res => {
                    var html = "";
                    res.forEach(function (item){
                        html += "<p>"+item.data.result+"</p>";
                        if (item.data.imageurl){
                            html += "<img src='"+item.data.imageurl+"' width='100%'>";
                        }
                        let responseObj = typeof res === 'string' ? JSON.parse(res) : res;
                        if (responseObj.success === false){
                            html += '<p><strong> '+responseObj.message+' </strong></p>';
                        }
                    })

                    $("#degisenSonuc").html(html);
                    var url = $("#export").attr("data-url");
                    $("#export").attr("href",url);
                });
            }

            async function ruhsat(type, arac) {
                var expertiseUuid = $("#expertiseUuid").val();

                $("#ruhsatSonuc").html('<center><span class="loader"></span></center>');
                if(type == "Sasi"){
                    var url = "{{ url($expertise['ruhsatSaseSorguUrl']) }}/" + expertiseUuid +  "/"+arac+"/Ruhsat";
                }else{
                    var url = "{{ url($expertise['ruhsatPlakaSorguUrl']) }}/" + expertiseUuid +  "/"+arac+"/Ruhsat";
                }

                $.get(url).then(res => {
                    borcSonucu = res;
                    var html = "<table class='table'>";
                    html +="<tr><th>Seri Numarası</th><td>"+res.serialnumber+"</td></tr>";
                    html +="<tr><th>Plaka</th><td>"+res.plate+"</td></tr>";
                    html +="<tr><th>Şasi</th><td>"+res.chassis+"</td></tr>";
                    html +="<tr><th>Motor Numarası</th><td>"+res.enginenumber+"</td></tr>";
                    html +="<tr><th>Model Yılı</th><td>"+res.modelyear+"</td></tr>";
                    html +="<tr><th>Marka </th><td>"+res.brand+"</td></tr>";
                    html +="<tr><th>Model </th><td>"+res.modelname+"</td></tr>";
                    html +="<tr><th>Yakıt Tipi </th><td>"+res.fueltype+"</td></tr>";
                    html +="<tr><th>Kayıt Tarihi </th><td>"+res.registerdate+"</td></tr>";
                    html +="<tr><th>Kayıt Başlangıç Tarihi </th><td>"+res.registerdateFrom+"</td></tr>";
                    html +="<tr><th>Yolcu Sayısı </th><td>"+res.passengercount+"</td></tr>";
                    html +="<tr><th>Kimlik Numarası </th><td>"+res.idnumber+"</td></tr>";
                    html +="<tr><th>Adı Soyadı </th><td>"+res.name+" "+res.surname+"</td></tr>";
                    html +="<tr><th>Renk </th><td>"+res.name+" "+res.color+"</td></tr>";
                    html +="<tr><th>Motor Kapasitesi </th><td>"+res.enginecapacity+"</td></tr>";
                    html +="<tr><th>Motor Gücü </th><td>"+res.enginepower+"</td></tr>";
                    html +="<tr><th>Sakınca Durumu </th><td>"+res.sakincadurumu+"</td></tr>";
                    html +="<tr><th>Kasko Değeri </th><td>"+res.kaskodegeri+"</td></tr>";
                    html += "</table>";

                    let responseObj = typeof res === 'string' ? JSON.parse(res) : res;
                    if (responseObj.success === false){
                        html += '<p><strong> '+responseObj.message+' </strong></p>';
                    }
                    $("#ruhsatSonuc").html(html);
                    var url = $("#export").attr("data-url");
                    $("#export").attr("href",url);
                });
            }

        })();
    </script>

    <style>
        .loader { width: 48px; height: 48px; display: inline-block; position: relative; }
        .loader::after, .loader::before { content: ''; box-sizing: border-box; width: 48px; height: 48px; border-radius: 50%; border: 2px solid #000; position: absolute; left: 0; top: 0; animation: animloader 2s linear infinite; }
        .loader::after { animation-delay: 1s; }
        @keyframes animloader {
            0% {
                transform: scale(0);
                opacity: 1;
            }
            100% {
                transform: scale(1);
                opacity: 0;
            }
        }
    </style>
@endpush
