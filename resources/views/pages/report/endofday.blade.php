@extends('pages.build')

@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('title','Cari Rapor')
@section('content')
    <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
        <a href="{{ route('reports',['type'=>'expertise']) }}" class="nav-link">Ekspertiz Raporları</a>
        @if($authUser->type == 'admin' || $authUser->zone_id > 0)
            <a href="{{ route('reports',['type'=>'stock']) }}" class="nav-link">Stok Raporları</a>
            <a href="{{ route('reports',['type'=>'branch']) }}" class="nav-link"><PERSON>i Raporları</a>
            @if($authUser->type == 'admin')
                <a href="{{ route('reports',['type'=>'query']) }}" class="nav-link"><PERSON>ar Raporları</a>
            @endif
        @endif
        <a href="{{ route('reports',['type'=>'customer']) }}" class="nav-link">Cari Raporları</a>
        <a href="{{ route('reports',['type'=>'car']) }}" class="nav-link">Araç Raporları</a>
        <a href="{{ route('reports',['type'=>'user']) }}" class="nav-link">Personel Raporları</a>
        <a href="{{ route('reports',['type'=>'endofdays']) }}" class="nav-link active">Gün Sonu Raporu</a>
    </nav>
    <form id="filterForm" method="get" action="{{ route('reports',['type'=>'endofdays']) }}">
        <div class="card">
            <div class="card-body">
                <div class="row">
                    <div class="col-xl-3 col-lg-3 col-12">
                        <div class="form-group">
                            <label>Başlangıç Tarihi</label>
                            <input type="date" class="form-control" name="start_date" value="{{ $filters['startDate'] }}">
                        </div>
                    </div>
                    <div class="col-xl-3 col-lg-3 col-12">
                        <div class="form-group">
                            <label>Bitiş Tarihi</label>
                            <input type="date" class="form-control" name="end_date" value="{{ $filters['endDate'] }}">
                        </div>
                    </div>
                    <div class="col-xl-3 col-lg-3 col-12">
                        <div class="form-group">
                            <label for="kupon_fiyat">Kupon Tutarını Sıfır Olarak göster</label>
                            <select name="kupon_fiyat" class="form-control" id="kupon_fiyat">
                                <option  @if($kupon_fiyat == 1) selected="" @endif value="1">Evet</option>
                                <option @if($kupon_fiyat == 2) selected="" @endif  value="2">Hayır</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-xl-3 col-lg-3 col-12">
                        <div class="form-group">
                            <label for="fiyat_ayri">Her Fiyatı Ayrı Göster</label>
                            <select name="fiyat_ayri" class="form-control" id="fiyat_ayri">
                                <option @if($fiyat_ayri == 2) selected="" @endif value="2">Hayır</option>
                                <option @if($fiyat_ayri == 1) selected="" @endif value="1">Evet</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-xl-3 col-lg-3 col-12">
                        <div class="form-group">
                            <label for="devir_bakiye">Devir Bakiyesi Eklensin</label>
                            <select name="devir_bakiye" class="form-control" id="devir_bakiye">
                                <option @if($devir_bakiye == 2) selected="" @endif value="2">Hayır</option>
                                <option @if($devir_bakiye == 1) selected="" @endif value="1">Evet</option>
                            </select>
                        </div>
                    </div>
                    <div class="col-xl-3 col-lg-3 col-12">
                        <div class="form-group">
                            <label>Bayi</label>
                            <select class="form-control select2" name="branch_id">
                                @foreach($branches as $filterBranch)
                                    <option @if($selected_branch == $filterBranch->id) selected @endif value="{{ $filterBranch->id }}">{{ $filterBranch->kisa_ad }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col mt-4">
                        <button class="btn btn-success">Listele</button>
                    </div>
                </div>
            </div>
        </div>
    </form>
    @php
    $total_amount = 0;
    $total_amount_nakit = 0;
    $total_count_nakit = 0;
    $total_amount_banka_kredi = 0;
    $total_count_banka_kredi = 0;
    $total_amount_plus_kart = 0;
    $total_count_plus_kart = 0;
    $total_gider_amount = 0;
    $total_expertise = 0;

    $plus_card_sales_count = 0;
    $plus_card_sales_amount = 0;
    $plus_card_sales_nakit_count = 0;
    $plus_card_sales_nakit_amount = 0;
    $plus_card_sales_banka_kredi_count = 0;
    $plus_card_sales_banka_kredi_amount = 0;

    @endphp
    <div class="row">
        <div class="card">
            <div class="card-body">
                <h3>Gelir Tablosu</h3>
                <div class="table-responsive">
                    <table class="table table-striped table-responsive">
                        <thead style="position: sticky;top: 0">
                        <tr>
                            <th>Cari Hesap Ünvan</th>
                            <th>Gelir Adı</th>
                            <th>Miktar</th>
                            <th>Fiyat</th>
                            @if(!empty($_GET['fiyat_ayri']) && $_GET['fiyat_ayri'] != 1)
                            <th>Satış Fiyat Ortalaması</th>
                            @endif
                            <th>Gelir Tutarı</th>
                            <th>Nakit</th>
                            <th>Nakit Adet</th>
                            <th>Kredi/Banka</th>
                            <th>Kredi/Banka Adet</th>
                            <th>Kupon</th>
                            <th>Kupon Adet</th>
                        </tr>
                        </thead>
                        <tbody class="filter-results-new">
                            @foreach($report_data as $r)
                            <tr>
                                <td></td>
                                <td>{{$r['name']}}</td>
                                <td>{{$r['count']}}</td>
                                <td>
                                    ₺{{number_format($r['amount'], 2, ',', '.')}}
                                </td>
                                @if(!empty($_GET['fiyat_ayri']) && $_GET['fiyat_ayri'] != 1 && !empty($r['fiyat']) && !empty($r['count']) && $r['count'] != 0)
                                    <td>₺{{ number_format(($r['fiyat'] / $r['count']), 2, ',', '.') }}</td>
                                @endif
                                <td>₺{{number_format($r['fiyat'], 2, ',', '.')}}</td>
                                <td>₺{{number_format($r['nakit_tutar'], 2, ',', '.')}}</td>
                                <td>{{$r['nakit_count']}}</td>
                                <td>₺{{number_format($r['kredi_banka_tutar'], 2, ',', '.')}}</td>
                                <td>{{$r['kredi_banka_count']}}</td>
                                <td>
                                    @if($kupon_fiyat == 2)
                                        ₺{{number_format($r['plus_kart_tutar'], 2, ',', '.')}}
                                    @endif
                                </td>
                                <td>{{$r['plus_kart_count']}}</td>
                            </tr>
                                @php
                                    $total_amount+=$r['fiyat'];
                                    $total_amount_nakit += $r['nakit_tutar'];
                                    $total_amount_banka_kredi += $r['kredi_banka_tutar'];
                                    $total_amount_plus_kart += $r['plus_kart_tutar'];
                                    $total_expertise = $total_expertise+$r['count'];

                                    $total_count_nakit = $total_count_nakit+$r['nakit_count'];
                                    $total_count_banka_kredi = $total_count_banka_kredi+$r['kredi_banka_count'];
                                    $total_count_plus_kart = $total_count_plus_kart+$r['plus_kart_count'];
                                @endphp
                            @endforeach
                        <tr>
                            <td></td>
                            <td><b>Toplam : </b></td>
                            <td class="text-blue"   ><b>{{$total_expertise}}</b></td>
                            @if(!empty($_GET['fiyat_ayri']) && $_GET['fiyat_ayri'] != 1)
                                <td></td>
                            @endif
                            <td><b>Toplam : </b></td>
                            <td class="text-blue"><b>₺{{number_format($total_amount, 2, ',', '.')}}</b></td>
                            <td class="text-blue"><b>₺{{number_format($total_amount_nakit,2,',','.')}}</b></td>
                            <td class="text-blue">{{$total_count_nakit}}</td>
                            <td class="text-blue"><b>₺{{number_format($total_amount_banka_kredi,2,',','.')}}</b></td>
                            <td class="text-blue"><b>{{$total_count_banka_kredi}}</b></td>
                            <td class="text-blue">
                                @if($kupon_fiyat == 2)
                                <b>₺{{number_format($total_amount_plus_kart,2,',','.')}}</b>
                                @endif
                            </td>
                            <td>{{$total_count_plus_kart}}</td>
                        </tr>
                        </tbody>
                    </table>
                </div>
                <h3 class="mt-3">Plus Card Satış Tablosu</h3>
                <div class="table-responsive">
                    <table class="table table-striped table-responsive">
                        <thead style="position: sticky;top: 0">
                            <tr>
                                <th>Tanım Adı</th>
                                <th>Satış Fiyatı</th>
                                <th>Satış Sayısı</th>
                                <th>Satış Miktarı</th>
                                <th>Nakit Satış Sayısı</th>
                                <th>Nakit Satış Miktarı</th>
                                <th>Banka/Kredi Satış Sayısı</th>
                                <th>Banka/Kredi Satış Miktarı</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach($plus_card_data as $plus_card)
                                <tr>
                                    <td>{{$plus_card['name']}}</td>
                                    <td>₺{{number_format($plus_card['definition_sales_amount'], 2, ',', '.')}}</td>
                                    <td>{{$plus_card['sales_count']}}</td>
                                    <td>₺{{number_format($plus_card['sales_amount'], 2, ',', '.')}}</td>
                                    <td>{{$plus_card['sales_nakit_count']}}</td>
                                    <td>₺{{number_format($plus_card['sales_nakit_amount'], 2, ',', '.')}}</td>
                                    <td>{{$plus_card['sales_banka_kredi_count']}}</td>
                                    <td>₺{{number_format($plus_card['sales_banka_kredi_amount'], 2, ',', '.')}}</td>

                                </tr>
                                @php
                                    $plus_card_sales_count = $plus_card_sales_count + $plus_card['sales_count'];
                                    $plus_card_sales_amount = $plus_card_sales_amount + $plus_card['sales_amount'];
                                    $plus_card_sales_nakit_count = $plus_card_sales_nakit_count + $plus_card['sales_nakit_count'];
                                    $plus_card_sales_nakit_amount = $plus_card_sales_nakit_amount + $plus_card['sales_nakit_amount'];
                                    $plus_card_sales_banka_kredi_count = $plus_card_sales_banka_kredi_count + $plus_card['sales_banka_kredi_count'];
                                    $plus_card_sales_banka_kredi_amount = $plus_card_sales_banka_kredi_amount + $plus_card['sales_banka_kredi_amount'];
                                @endphp
                            @endforeach
                            <tr>
                                <td></td>
                                <td ><b>Toplam : </b></td>
                                <td class="text-blue"><b>{{$plus_card_sales_count}}</b></td>
                                <td class="text-blue"><b>₺{{number_format($plus_card_sales_amount, 2, ',', '.')}}</b></td>
                                <td class="text-blue"><b>{{$plus_card_sales_nakit_count}}</b></td>
                                <td class="text-blue"><b>₺{{number_format($plus_card_sales_nakit_amount, 2, ',', '.')}}</b></td>
                                <td class="text-blue"><b>{{$plus_card_sales_banka_kredi_count}}</b></td>
                                <td class="text-blue"><b>₺{{number_format($plus_card_sales_banka_kredi_amount, 2, ',', '.')}}</b></td>
                            </tr>
                        </tbody>
                    </table>
                </div>
                <h3 class="mt-3">Gider Tablosu</h3>
                <div class="table-responsive">
                    <table class="table table-striped table-responsive">
                        <thead style="position: sticky;top: 0">
                        <tr>
                            <th>Cari Hesap Ünvan</th>
                            <th>Gider Adı</th>
                            <th>Miktar</th>
                            <th>Fiyat</th>
                            <th>Gider Tutarı</th>
                            <th>Nakit</th>
                            <th>Kredi/Banka</th>
                            <th>Kupon</th>
                        </tr>
                        </thead>
                        <tbody class="filter-results-new">
                            @foreach($expense as $expens)
                                @foreach($expens as $e)
                                    <tr>
                                        <td>{{$e->getPlusCard->getCustomer->unvan ?? ''}}</td>
                                        <td>Hediye Puan</td>
                                        <td>{{$e->puan}}</td>
                                        <td>₺{{number_format($e->unit_price,2,',','.')}}</td>
                                        <td>₺{{number_format($e->puan * $e->unit_price,2,',','.')}}</td>
                                        <td></td>
                                        <td></td>
                                        <td></td>
                                    </tr>
                                    @php
                                        $total_gider_amount += $e->puan * $e->unit_price;
                                    @endphp
                                @endforeach
                            @endforeach
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-blue"><b>Toplam : </b></td>
                            <td class="text-blue"><b>₺{{number_format($total_gider_amount, 2, ',', '.')}}</b></td>
                            <td></td>
                            <td></td>
                            <td></td>
                        </tr>
                        <tr>
                            <td></td>
                            <td></td>
                            <td></td>
                            <td class="text-blue"><b>Kalan : </b></td>
                            <td class="text-blue"><b>₺{{number_format($total_amount-$total_gider_amount, 2, ',', '.')}}</b></td>
                            <td class="text-blue"><b>₺{{number_format($total_amount_nakit,2,',','.')}}</b></td>
                            <td class="text-blue"><b>₺{{number_format($total_amount_banka_kredi,2,',','.')}}</b></td>
                            <td class="text-blue">
                                @if($kupon_fiyat == 2)
                                    <b>₺{{number_format($total_amount_plus_kart,2,',','.')}}</b>
                                @endif
                            </td>
                        </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/js/select2.min.js"></script>

    <script>
       $(document).ready(function(){
           $(".select2").select2({
               placeholder: "Ara.."
           });
       })
    </script>
@endpush
