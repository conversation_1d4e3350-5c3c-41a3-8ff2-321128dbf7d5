<div class="row yanal-kayma yatay rapor sonuc-sayfa-bg">
    <div class="sonuc-header">
        <div class="sonuc-logo">
            <img src="/assets/isemri_logo.png">
            <h6 style="color: darkorange">0850 354 54 17 / 0216 302 54 17</h6>
            <h6 class="text-danger">www.umranoto.com</h6>
            <b style="font-size: .5rem">Rapor Basım Tarihi / Saati / Sırası:</b>
            <h8 style="color: #333335;"> {{ now()->format('d.m.Y') }} / {{ now()->format('H:i') }} / {{ $expertise['download_count'] }}</h8>
        </div>
        <div class="sonuc-info">
            <div class="sonuc-first-tab">
                <div class="sonuc-first-tab-item">
                    <b>Plaka</b><br>
                    <span class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['plaka'] }}</span>
                    <span class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['plaka']) && strlen($expertise['plaka']) > 3 ? substr($expertise['plaka'], 0, 2) . '*****' . substr($expertise['plaka'], -1) : '' }}</span>
                </div>
                <div class="sonuc-first-tab-item">
                    <b>Model Yılı</b><br>
                    {{ $expertise['model_yili'] }}
                </div>
                <div class="sonuc-first-tab-item">
                    <b>Şase No</b><br>
                    <span class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['sase_no'] }}</span>
                    <span class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['sase_no'])  && strlen($expertise['sase_no']) > 3 ? substr($expertise['sase_no'], 0, 2) . '*****' . substr($expertise['sase_no'], -1) : '' }}</span>
                </div>
                <div class="sonuc-first-tab-item">
                    <b>Yetkili Hizmet Yeri</b><br>
                    {{ $expertise['getBranch'] }}
                </div>
            </div>
            <div class="sonuc-second-tab">
                <span style="color: #fff">Yanal Kayma Testi Sonuç Sayfası</span>
                <span>UMRAN Bağımsız Araç Ekspertiz Merkezleri</span>
            </div>
            <div class="sonuc-third-tab">
                <div class="sonuc-first-tab-item">
                    <b>Marka:</b>
                    {{ $expertise['getMarka'] }} {{ $expertise['getModel'] }}
                </div>
                <div class="sonuc-first-tab-item">
                    <b>@if($expertise['km_type'] == 1) KM @else Mil @endif:</b>
                    {{ number_format($expertise['km'] ?? 0,0,'.','.') }}
                </div>
                <div class="sonuc-first-tab-item">
                    <b>Belge No:</b>
                    <span style="font-size: 1.4rem" class="text-danger">{{ $expertise['belge_no'] }}</span>
                </div>
            </div>
            <div class="sonuc-third-tab">
                <div class="sonuc-first-tab-item">
                    <b>Müşteri:</b>
                    <span class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['getAlici'] }}</span>
                    <span class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['getAlici']) && strlen($expertise['getAlici']) > 3 ? substr($expertise['getAlici'], 0, 2) . '*****' . substr($expertise['getAlici'], -1) : '' }}</span>
                </div>
                <div class="sonuc-first-tab-item">
                    <b>GSM:</b>
                    <span class="filtresiz @if(isset($mask) && $mask == 'filtreli') d-none @endif">{{ $expertise['getAliciTel'] }}</span>
                    <span class="filtreli @if(isset($mask) && $mask == 'filtresiz') d-none @endif">{{ !empty($expertise['getAliciTel'])  && strlen($expertise['getAliciTel']) > 3 ? substr($expertise['getAliciTel'], 0, 2) . '*****' . substr($expertise['getAliciTel'], -1) : '' }}</span>
                </div>
                <div class="sonuc-first-tab-item">
                    <div style="background-color: lightgoldenrodyellow;padding: 3px 7px"><i class="fa fa-clock"></i> {{ $expertise['created_at'] }}</div>
                </div>
            </div>
        </div>
    </div>
    <div style="background-color: #f8f8f8;width: 100%" class="d-flex align-items-center justify-content-center">
        <img src="/assets/arac_alti.png" style="width: 20%" class="img-fluid arac-alti-bir kaporta-gorsel">
        <div class="d-flex" style="flex-direction: column">
            <span>
                <b>Yanal Kayma ( Ön Dingil )</b> :
                <span @if((float)$expertise['on_yanal_kayma'] <=7) class="text-success-rapor" @elseif((float)$expertise['on_yanal_kayma'] <=14) class="text-warning-rapor" @else class="text-danger-rapor" @endif style="font-size: 2rem">
                    {{ $expertise['on_yanal_kayma'] }} m/km
                </span>
            </span>
            <span class="mt-5">
                <b>Yanal Kayma ( Arka Dingil )</b> :
                <span @if((float)$expertise['arka_yanal_kayma'] <=7) class="text-success-rapor" @elseif((float)$expertise['arka_yanal_kayma'] <=14) class="text-warning-rapor" @else class="text-danger-rapor" @endif style="font-size: 2rem">
                    {{ $expertise['arka_yanal_kayma'] }} m/km
                </span>
            </span>
        </div>
    </div>
    <div style="background-color: #fff;width: 100%;text-align: right;padding: 1rem" class="d-flex justify-content-end">
        <div class="color-bar" style="float: right">
            <div class="text">0</div>
            <div class="line green"></div>
            <div class="text">7</div>
            <div class="line yellow"></div>
            <div class="text">14</div>
            <div class="line red"></div>
            <div class="text">m/km</div>
        </div>
    </div>
    <div class="d-flex justify-content-between" style="font-size: .7rem">
        <span><span class="text-danger">KBR</span> {{ $expertise['bayiAdres'] }}</span>
        <span>{{ $expertise['bayiVergiDairesi'] }} VERGİ DAİRESİ {{ $expertise['bayiVergiNo'] }} / MERSİS : {{ $expertise['bayiMersis'] }}</span>
    </div>
</div>