@extends('pages.build')
@section('title',$expertise['plaka'] . '-' . $expertise['belge_no'])
@push('css')

@endpush
@section('content')
    @php

        $electrical_values = array();
    @endphp
    <div id="divOnGosterim">
       @include('pages.expertise.rapor.content',['expertise'=>$expertise])
    </div>
    <div id="divRaporEkrani"></div>
    @if(!isset($_GET['process']))
        <div class="col-12 d-flex justify-content-center print-buttons">
            {{-- <button class="btn btn-danger m-3" onclick="window.print()"><i class="fa fa-print"></i> Yazdır</button> --}}
            @php
                $user = $authUser;
                $userRoles = $user->getUserRoleGroup->getRoles;
                $isAdmin = $user->type == 'admin';
                $isGoprint = !empty($_GET['goprint']) && $_GET['goprint'] == 1 && !empty($_GET['refuuid']);
                $isEditable = !$expertise['is_old'] || $expertise['created_at'] > now()->subHour();
                $kvkkAllowed = $expertise['kvkk_durum'] == "false" || $isAdmin || $isGoprint;
                $isOver = !empty($expertise['is_it_over']) && $expertise['is_it_over'] == true;
                $hasFtpRole = ($userRoles->whereIn('key', ['ftp_expertise'])->isNotEmpty()) ? true : false;
                $hasPrintRole = ($userRoles->whereIn('key', ['print_expertise'])->isNotEmpty()) ? true : false;
            @endphp

            @if($isAdmin || $hasFtpRole || $hasPrintRole || $isGoprint)
                @if($expertise['employee_downloaded'] == 0 || $isAdmin || $isGoprint)
                    @if($hasPrintRole || $isAdmin || $isGoprint)
                        @if($isEditable || $isAdmin)
                            @if($kvkkAllowed || $isAdmin)
                                <a href="{{ url()->current() }}?process=print&type=filtresiz" target="_blank" class="btn btn-danger m-3"><i class="fe bi-file-earmark-pdf"></i> Filtresiz Yazdır</a>
                            @endif
                        @endif
                        <a href="{{ url()->current() }}?process=print&type=filtreli" target="_blank" class="btn btn-danger m-3"><i class="fe bi-file-earmark-pdf"></i> Filtreli Yazdır</a>
                    @endif
                    @if($hasFtpRole)
                        <button class="btn btn-success m-3 ftp_ok"><i class="fe bi-file-earmark-pdf"></i> FTP'e Gönder</button>
                    @endif
                @else
                    <button type="button" class="btn btn-success m-3"><i class="fe bi-file-earmark-pdf"></i> Personel FTP Gönderdi</button>
                @endif

            @endif
        @if($isEditable && ($isAdmin || $hasFtpRole || $hasPrintRole))
            <a href="{{ route('expertise.taslakAracResmi', ['uuid' => $expertise['uuid']]) }}" target="_blank" class="btn btn-warning m-3">
                <i class="fe bi-eye"></i> Taslak Göster
            </a>
        @endif
        </div>
    @endif

    @if(empty($_GET['type']))
    <div class="audio_div">
        @if(!empty($expertise['is_it_over']) && $expertise['is_it_over'] == true )
            <div class="col-12">
                <button class="btn btn-danger btn-sm" id="startRecord">Kaydı Başlat</button>
                <button class="btn btn-danger btn-sm" id="stopRecord" disabled>Kaydı Durdur</button>
            </div>
            <div class="col-12 mt-3">
                <audio id="audio" src="/storage/{{ $expertise['audio_url'] }}" controls></audio>
            </div>
        @elseif(!empty($expertise['audio_url']))
            <audio id="audio" src="/storage/{{ $expertise['audio_url'] }}" controls></audio>
        @endif
    </div>
    @endif
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>

    <script src="/assets/js/html2pdf.bundle.js"></script>
    <script>
        const Toast = Swal.mixin({
            toast: true,
            position: "top-end",
            showConfirmButton: false,
            timer: 3000,
            timerProgressBar: true,
            didOpen: (toast) => {
                toast.onmouseenter = Swal.stopTimer;
                toast.onmouseleave = Swal.resumeTimer;
            }
        });

        $(document).ready(function(){

            @if(!isset($mask))
            @if($authUser->type == 'admin')
            $('.filtreli').addClass('d-none') // Filtreli Gizle
            $('.filtresiz').removeClass('d-none') // Filtreli Gizle
            @else
            $('.filtresiz').addClass('d-none') // Filtresiz Gizle
            $('.filtreli').removeClass('d-none') // Filtresiz Gizle
            @endif
            @endif


            setTimeout(function (){
                @if(isset($_GET['process']))
                    @if($_GET['type'] == 'filtreli')
                    ExportPdf('filtreli')
                    @else
                    ExportPdf('filtresiz')
                    @endif
                @endif

                @if(!empty($_GET['goprint']) && $_GET['goprint'] == 1)
                // $('html, body').animate({scrollTop: $(document).height()}, 'slow');
                ExportPdf()
                @endif
            },1000)
        })
        function ExportPdf(type = "filtresiz",page = "rapor") {
            @if(!empty($_GET['goprint']) && $_GET['goprint'] == 1 && !empty($_GET['refuuid']))
            $.ajax({
                url: "{{ route('api.createOldExpertisDowland') }}",
                type: "post",
                data: {
                    _token:'{{csrf_token()}}',
                    ref_uuid:'{{$_GET['refuuid']}}',
                    uuid:'{{$expertise['uuid']}}'
                },
                success: function (response) {
                    if(response.success == "true"){

                        Toast.fire({
                            icon: "success",
                            title: "Bir önceki sayfaya dönüp kaldığınız yerden devam edebilirsiniz.",
                        });
                    }else{
                        Toast.fire({
                            icon: "error",
                            title: "Bir Hata Oluştu Lütfen Sayfayı Yenileyin!"
                        });
                    }
                },
            });
            @else
            $.ajax({
                url: "{{ route('api.PrintCikisTarihi') }}",
                type: "post",
                data: {
                    _token:'{{csrf_token()}}',
                    uuid:'{{$expertise['uuid']}}'
                },
                success: function (response) {
                },
            });
            @endif
            $.ajax({
                url: "{{ route('api.expertiseReportDownload') }}",
                type: "post",
                data: {
                    _token:'{{csrf_token()}}',
                    uuid:'{{$expertise['uuid']}}'
                },
                success: function (response) {
                },
            });
            if(type == "filtreli"){
                $('.filtreli').removeClass('d-none')
                $('.filtresiz').addClass('d-none')
            }else{
                $('.filtreli').addClass('d-none')
                $('.filtresiz').removeClass('d-none')
            }
            @if($authUser->type != 'admin')
            $.ajax({
                url: "{{ route('api.setExpertiseDownloaded') }}",
                type: "post",
                data: {
                    _token:'{{csrf_token()}}',
                    uuid:'{{$expertise['uuid']}}'
                },
                success: function (response) {
                    console.log(response)
                },
            });
            @endif

            @php $expertiseStockIDS = \Illuminate\Support\Arr::pluck($expertise['getStocks'],'id') @endphp
            @if(in_array(188,$expertiseStockIDS) || in_array(189,$expertiseStockIDS) || in_array(190,$expertiseStockIDS) || in_array(191,$expertiseStockIDS))
            $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
            $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
            if ($('.kaporta-boya').length > 0){
                let kaportaBoya = $('.kaporta-boya').clone().removeClass('kaporta-boya');
                kaportaBoya.find("#canvas").remove()
                kaportaBoya.find("#myImage").removeAttr("id").css('display','block')
                for (let i = 0; i < 3; i++) {
                    kaportaBoya.clone().appendTo("#divRaporEkrani");
                }
            }
            if ($('.motor-mekanik').length > 0){
                $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
            }
            @elseif(in_array(217,$expertiseStockIDS))
                $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
                $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
                if ($('.kaporta-boya').length > 0){
                    let kaportaBoya = $('.kaporta-boya').clone().removeClass('kaporta-boya');
                    kaportaBoya.find("#canvas").remove()
                    kaportaBoya.find("#myImage").removeAttr("id").css('display','block')
                    for (let i = 0; i < 3; i++) {
                        kaportaBoya.clone().appendTo("#divRaporEkrani");
                    }
                }
                if ($('.motor-mekanik').length > 0){
                    $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                    $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                    $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                }
                if ($('.conta').length > 0){
                    $('.conta').clone().removeClass('conta').appendTo("#divRaporEkrani");
                    $('.conta').clone().removeClass('conta').appendTo("#divRaporEkrani");
                }

            @elseif(in_array(187,$expertiseStockIDS) || in_array(227, $expertiseStockIDS ) || in_array(229, $expertiseStockIDS))
            $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
            $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
            if ($('.kaporta-boya').length > 0){
                 let kaportaBoya = $('.kaporta-boya').clone().removeClass('kaporta-boya');
                kaportaBoya.find("#canvas").remove()
                kaportaBoya.find("#myImage").removeAttr("id").css('display','block')
                for (let i = 0; i < 3; i++) {
                    kaportaBoya.clone().appendTo("#divRaporEkrani");
                }
            }
            @elseif(in_array(186,$expertiseStockIDS) || in_array(226, $expertiseStockIDS) || in_array(228, $expertiseStockIDS))
            $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
            $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
            if ($('.motor-mekanik').length > 0){
                $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
            }
            @elseif(in_array(7,$expertiseStockIDS) || in_array(15,$expertiseStockIDS) || in_array(150,$expertiseStockIDS) || in_array(16,$expertiseStockIDS) || in_array(218,$expertiseStockIDS) 
                || in_array(222,$expertiseStockIDS)
                || in_array(223,$expertiseStockIDS)
                || in_array(224,$expertiseStockIDS)
                || in_array(225,$expertiseStockIDS)
            )
            $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
            $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
            if ($('.kaporta-boya').length > 0){
                let kaportaBoya = $('.kaporta-boya').clone().removeClass('kaporta-boya');
                kaportaBoya.find("#canvas").remove()
                kaportaBoya.find("#myImage").removeAttr("id").css('display','block')
                for (let i = 0; i < 3; i++) {
                    kaportaBoya.clone().appendTo("#divRaporEkrani");
                }
            }
            if ($('.motor-mekanik').length > 0){
                $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
            }
            @elseif(in_array(220,$expertiseStockIDS) || in_array(221,$expertiseStockIDS))
                $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
                $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
                $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
                if ($('.kaporta-boya').length > 0){
                    let kaportaBoya = $('.kaporta-boya').clone().removeClass('kaporta-boya');
                    kaportaBoya.find("#canvas").remove()
                    kaportaBoya.find("#myImage").removeAttr("id").css('display','block')
                    for (let i = 0; i < 3; i++) {
                        kaportaBoya.clone().appendTo("#divRaporEkrani");
                    }
                }
                if ($('.motor-mekanik').length > 0){
                    $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                    $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                    $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                }
            @elseif(in_array(9,$expertiseStockIDS))
            $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
            $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
            if ($('.kaporta-boya').length > 0){
                 let kaportaBoya = $('.kaporta-boya').clone().removeClass('kaporta-boya');
                kaportaBoya.find("#canvas").remove()
                kaportaBoya.find("#myImage").removeAttr("id").css('display','block')
                for (let i = 0; i < 3; i++) {
                    kaportaBoya.clone().appendTo("#divRaporEkrani");
                }
            }
            @elseif(in_array(10,$expertiseStockIDS))
            $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
            $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
            if ($('.motor-mekanik').length > 0){
                $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
                $('.motor-mekanik').clone().removeClass('motor-mekanik').appendTo("#divRaporEkrani");
            }
            @elseif(in_array(209,$expertiseStockIDS))
                $('.is-emri').clone().removeClass('is-emri').appendTo("#divRaporEkrani");
            @endif

            @if(in_array(261,\Illuminate\Support\Arr::pluck($expertise['getStocks'],'campaign_id')) 
                || in_array(209, $expertiseStockIDS) 
                || in_array(218, $expertiseStockIDS) 
                || in_array(220, $expertiseStockIDS) 
                || in_array(221, $expertiseStockIDS) 
                || in_array(222, $expertiseStockIDS)
                || in_array(223, $expertiseStockIDS)
                || in_array(224, $expertiseStockIDS)
                || in_array(225, $expertiseStockIDS)
                || in_array(226, $expertiseStockIDS)
                || in_array(227, $expertiseStockIDS)
                || in_array(228, $expertiseStockIDS)
                || in_array(229, $expertiseStockIDS)
            )
                if ($('.conta').length > 0){
                    $('.conta').clone().removeClass('conta').appendTo("#divRaporEkrani");
                    $('.conta').clone().removeClass('conta').appendTo("#divRaporEkrani");
                }
            @endif
            window.print()
            @php $sonucYazdir = false @endphp
            $("#divRaporEkrani").html('')
            @if($expertise['hasBrake'] || $expertise['hasSubControls'])
                if ($('.yanal-kayma').length > 0){
                    $('.yanal-kayma').clone().removeClass('yanal-kayma').appendTo("#divRaporEkrani");
                }
                if ($('.fren-testi').length > 0){
                    $('.fren-testi').clone().removeClass('fren-testi').appendTo("#divRaporEkrani");
                }

                if ($('.suspansiyon').length > 0){
                    $('.suspansiyon').clone().removeClass('suspansiyon').appendTo("#divRaporEkrani");
                }

                @if(in_array(220,$expertiseStockIDS) || in_array(221,$expertiseStockIDS))
                    if ($('.yanal-kayma').length > 0){
                        $('.yanal-kayma').clone().removeClass('yanal-kayma').appendTo("#divRaporEkrani");
                    }
                    if ($('.fren-testi').length > 0){
                        $('.fren-testi').clone().removeClass('fren-testi').appendTo("#divRaporEkrani");
                    }

                    if ($('.suspansiyon').length > 0){
                        $('.suspansiyon').clone().removeClass('suspansiyon').appendTo("#divRaporEkrani");
                    }
                @endif

                @php $sonucYazdir = true @endphp
            @endif
            @if($sonucYazdir)
                window.print()
                $("#divRaporEkrani").html('')
            @endif

        }

        $('.ftp_ok').click(function(){
            var thisbtn = $(this)

            Swal.fire({
                title: "Emin misiniz?",
                text: "Ekspertiz FTP'e gönderilecek",
                icon: "warning",
                showCancelButton: true,
                confirmButtonColor: "#1aafac",
                cancelButtonColor: "#3085d6",
                confirmButtonText: "Evet, FTP'e Gönder!",
                cancelButtonText: "Hayır, Gönderme!"
            }).then((result) => {
                if (result.isConfirmed) {
                    thisbtn.attr('disabled','')
                    $('.filtreli').addClass('d-none')
                    $('.filtresiz').removeClass('d-none')
                    let html = $('#divRaporEkrani').html();
                    if (html.length < 100)
                        html = $('#divOnGosterim').html();
                    var data_html = "<style>"+$('style').html()+"</style>"+html;
                    $('.filtreli').removeClass('d-none')
                    $('.filtresiz').addClass('d-none')
                    var maskes_data_html ="<style>"+$('style').html()+"</style>"+html;
                    $.ajax({
                        url: "{{ route('api.expertiseFtpOk') }}",
                        type: "post",
                        data: {
                            _token:'{{csrf_token()}}',
                            uuid:'{{$expertise['uuid']}}',
                            html_data:data_html,
                            maskes_data_html:maskes_data_html,
                        },
                        success: function (response) {
                            if(response.success == 'true'){
                                thisbtn.remove()
                                Toast.fire({
                                    icon: "success",
                                    title: "Ekspertiz Ftp'e gönderildi",
                                });
                                window.location = "/expertises";
                            }else{
                                thisbtn.removeAttr('disabled')
                                Toast.fire({
                                    icon: "error",
                                    title: response.message,
                                });
                            }
                        },
                    });
                }
            });
        })

        @if(empty($_GET['type']) && !empty($expertise['is_it_over']) && $expertise['is_it_over'] == true )
        let mediaRecorder;
        let audioChunks = [];

        document.getElementById("startRecord").addEventListener("click", async () => {
            // Kullanıcının mikrofonuna erişim sağlama
            const stream = await navigator.mediaDevices.getUserMedia({ audio: true });
            mediaRecorder = new MediaRecorder(stream);

            // Ses verileri geldikçe bu verileri bir diziye ekleyin
            mediaRecorder.ondataavailable = event => {
                audioChunks.push(event.data);
            };

            // Kayıt durduğunda ses verilerini birleştirip audio elementinde oynat
            mediaRecorder.onstop = () => {
                const audioBlob = new Blob(audioChunks, { type: 'audio/wav' });
                const audioUrl = URL.createObjectURL(audioBlob);
                const audio = document.getElementById("audio");
                audio.src = audioUrl;

                const formData = new FormData();
                formData.append("audioFile", audioBlob, "recording.wav");
                formData.append("uuid", '{{$expertise['uuid']}}');
                formData.append("_token", '{{ csrf_token() }}');

                $.ajax({
                    url: '{{ route('voiceRecord') }}', // Sunucu tarafı işlemci URL'si
                    type: 'POST',
                    data: formData,
                    processData: false,  // jQuery'nin data'yı işlemesini engelle
                    contentType: false,  // İçerik tipi başlığını jQuery'nin ayarlamasını engelle
                    success: function(data) {

                    },
                    error: function(error) {
                        console.error('Error:', error);
                    }
                });

                audioChunks = [];
            };

            // Kaydı başlat ve düğmeleri güncelle
            mediaRecorder.start();
            document.getElementById("startRecord").disabled = true;
            document.getElementById("stopRecord").disabled = false;
        });

        document.getElementById("stopRecord").addEventListener("click", () => {
            // Kaydı durdur ve düğmeleri güncelle
            mediaRecorder.stop();
            document.getElementById("startRecord").disabled = false;
            document.getElementById("stopRecord").disabled = true;
        });
        @endif

        @if($expertise['coordinates'] != [])
        document.addEventListener('DOMContentLoaded', function () {
            var canvas = document.getElementById('canvas');
            var ctx = canvas.getContext('2d');
            var image = document.getElementById('myImage');
            var drawingStack = [];
            var currentStep = -1;

            @if($expertise['coordinates'] != [])
            @foreach(json_decode($expertise['coordinates']['coordinates'],true) as $expertiseCoordinate)
            drawingStack.push({ x: {{ $expertiseCoordinate['x'] }}, y: {{ $expertiseCoordinate['y'] }}, color: '{{ __('arrays.bodywork_colors')[$expertiseCoordinate['color']] }}' });
            currentStep++;
            @endforeach
            @endif

            canvas.width = image.width;
            canvas.height = image.height;

            // Resmi canvas'a çiz
            ctx.drawImage(image, 0, 0, canvas.width, canvas.height);

            if (ctx.getImageData(0, 0, canvas.width, canvas.height).data.every(value => value === 0)) {
                Toast.fire({
                    icon: "error",
                    title: "Kaporta Resmi Çizilemedi! Sayfa Yenileniyor!"
                });
                setTimeout(function (){
                    window.location.reload()
                },1000)
            }

            image.style.display = 'none'

            function draw() {
                ctx.clearRect(0, 0, canvas.width, canvas.height);
                ctx.drawImage(image, 0, 0, canvas.width, canvas.height);

                let scaleX, scaleY;

                if ({{ $expertise['coordinates']['image_client_width'] }} < canvas.width) {
                    scaleX = {{ $expertise['coordinates']['image_client_width'] }} / canvas.width;
                    scaleY = {{ $expertise['coordinates']['image_client_height'] }} / canvas.height;
                } else {
                    scaleX = canvas.width / {{ $expertise['coordinates']['image_client_width'] }};
                    scaleY = canvas.height / {{ $expertise['coordinates']['image_client_height'] }};
                }

                console.log(scaleX,scaleY)

                for (var i = 0; i <= currentStep; i++) {
                    var step = drawingStack[i];
                    ctx.fillStyle = step.color;

                    // Koordinatları ölçekleyin
                    var scaledX = step.x * scaleX;
                    var scaledY = step.y * scaleY;

                    ctx.fillRect(scaledX, scaledY, 12, 12);
                }


                image.src = canvas.toDataURL();
            }
            draw();
        });
        @endif

    </script>

@endpush
