@extends('pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Bayi Düzenle')
@section('content')
    <button type="button" class="btn btn-sm btn-danger" onclick="ExportPdf()"><i class="bi bi-printer"></i> Yazdır</button>
    <form method="post" action="{{ route('branches.update',$branch) }}" enctype="multipart/form-data">@csrf @method('put')
        <div id="divRaporEkrani" class="row">
            <div class="col-md-6">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="subeBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#subeBilgiler" aria-expanded="true"
                                    aria-controls="subeBilgiler">
                                <div class="col-md-6">Bayi Bilgileri</div>
                                <div class="col-md-5 text-end">{{ $branch->kisa_ad }} @if($branch->category_id) > {{ $branch->category?->name }} @endif</div>
                            </button>
                        </h2>
                        <div id="subeBilgiler" class="accordion-collapse collapse show" aria-labelledby="subeBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row ortaklar">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Hizmet Verdiği İl</label>
                                        <select name="il_kodu" class="select2">
                                            @foreach($cities as $city)
                                                <option @if($city->id == $branch->il_kodu) selected @endif value="{{ $city->id }}">{{ $city->title }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Hizmet Verdiği İlçe</label>
                                        <select name="ilce_kodu" class="select2">

                                        </select>
                                    </div>
                                    <div class="col-xl-6 d-none mt-2">
                                        <label class="form-label">Bayi Kodu</label>
                                        <input type="text" class="form-control form-control-sm" name="kod" readonly placeholder="Bayi Kodu" value="{{ $branch->kod }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Kısa Adı <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control form-control-sm" name="kisa_ad" required placeholder="Kısa Adı" value="{{ $branch->kisa_ad }}" >
                                    </div>
                                    <div class="col-xl-5 mt-2">
                                        <label class="form-label">Bayi Kategori <span class="text-danger">*</span></label>
                                        <select required class="select2" name="branch_category">
                                            <option value="0">Seçilmedi</option>
                                            @foreach($branchCategories as $category)
                                                <option @if($branch->category_id == $category->id) selected @endif value="{{ $category->id }}">{{ $category->name }}</option>
                                            @endforeach
                                        </select>
                                    </div>
                                    <div class="col-xl-1 mt-2">
                                        <span class="info-hover" title="@foreach($branchCategories as $category) # {{ $category->name }} # -> @foreach($category->userLimits as $limit) {{ $limit->group->name }}:{{ $limit->qty }}, @endforeach @endforeach" style="margin-top: 32px;display: block;"><i class="fa fa-info"></i></span>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Unvan <span class="text-danger">*</span></label>
                                        <input type="text" class="form-control form-control-sm" name="unvan" required placeholder="Unvan" value="{{ $branch->unvan }}" >
                                    </div>
                                    @forelse($partners as $key => $partner)
                                        <div class="col-xl-4 mt-2">
                                            <label class="form-label">Ortak/Yetkili</label>
                                            <select class="select2" name="partner_id[]">
                                                <option value="0">Seçilmedi</option>
                                                @foreach($branch->activeOwners() as $owner)
                                                    <option @if($partner->user_id == $owner->id) selected @endif value="{{ $owner->id }}">{{ $owner->fullName() }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-xl-4 mt-2">
                                            <label class="form-label">Yüzde(%)</label>
                                            <input type="text" class="form-control form-control-sm" name="percent[]" value="{{ $partner->percent }}" placeholder="Yüzde(%)">
                                        </div>
                                        <div class="col-xl-4 mt-2 ortak-yuzde">
                                            <label class="form-label">Hakediş(₺)</label>
                                            <input type="text" class="form-control form-control-sm" disabled placeholder="0.00₺">
                                        </div>
                                    @empty
                                        <div class="col-xl-4 mt-2">
                                            <label class="form-label">Ortak/Yetkili</label>
                                            <select class="select2" name="partner_id[]">
                                                <option value="0">Seçilmedi</option>
                                                @foreach($branch->activeOwners() as $owner)
                                                    <option value="{{ $owner->id }}">{{ $owner->fullName() }}</option>
                                                @endforeach
                                            </select>
                                        </div>
                                        <div class="col-xl-4 mt-2">
                                            <label class="form-label">Yüzde(%)</label>
                                            <input type="text" class="form-control form-control-sm" name="percent[]" placeholder="Yüzde(%)">
                                        </div>
                                        <div class="col-xl-4 mt-2 ortak-yuzde">
                                            <label class="form-label">Hakediş(₺)</label>
                                            <input type="text" class="form-control form-control-sm" disabled placeholder="0.00₺">
                                        </div>
                                    @endforelse
                                    <div class="col-xl-4 offset-xl-8">
                                        <button type="button" class="btn btn-success mt-3 btn-sm ortak-ekle">Ortak Ekle</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="adresHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#adres" aria-expanded="true"
                                    aria-controls="adres">
                                Adres Bilgileri
                            </button>
                        </h2>
                        <div id="adres" class="accordion-collapse collapse show" aria-labelledby="adresHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Mahalle</label>
                                        <input type="text" class="form-control form-control-sm" name="mahalle" placeholder="Mahalle" value="{{ $branch->mahalle }}" >
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Cadde</label>
                                        <input type="text" class="form-control form-control-sm" name="cadde" placeholder="Cadde" value="{{ $branch->cadde }}" >
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Sokak</label>
                                        <input type="text" class="form-control form-control-sm" name="sokak" placeholder="Sokak" value="{{ $branch->sokak }}" >
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Semt</label>
                                        <input type="text" class="form-control form-control-sm" name="semt" placeholder="Semt" value="{{ $branch->semt }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Lat</label>
                                        <input type="text" class="form-control form-control-sm" name="lat" placeholder="Lat" value="{{ $branch->lat }}" >
                                    </div>
{{--                                    <div class="col-xl-6 mt-2">--}}
{{--                                        <label class="form-label">İlçe</label>--}}
{{--                                        <input type="text" class="form-control form-control-sm" name="ilce" placeholder="İlçe" value="{{ $branch->ilce }}" >--}}
{{--                                    </div>--}}
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Lng</label>
                                        <input type="text" class="form-control form-control-sm" name="lng" placeholder="Lng" value="{{ $branch->lng }}" >
                                    </div>
{{--                                    <div class="col-xl-6 mt-2">--}}
{{--                                        <label class="form-label">İl</label>--}}
{{--                                        <input type="text" class="form-control form-control-sm" name="il" placeholder="İl" value="{{ $branch->il }}" >--}}
{{--                                    </div>--}}
{{--                                    <div class="col-xl-6 mt-2">--}}
{{--                                        <label class="form-label">İl Kodu</label>--}}
{{--                                        <input type="text" class="form-control form-control-sm" name="il_kodu" placeholder="İl Kodu" value="{{ $branch->il_kodu }}" >--}}
{{--                                    </div>--}}
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Konum</label>
                                        <input type="text" class="form-control form-control-sm" name="konum" placeholder="Konum" value="{{ $branch->konum }}" >
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="iletisimBilgileriHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#iletisimBilgileri" aria-expanded="true"
                                    aria-controls="iletisimBilgileri">
                                İletişim Bilgileri
                            </button>
                        </h2>
                        <div id="iletisimBilgileri" class="accordion-collapse collapse show" aria-labelledby="iletisimBilgileriHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Telefon</label>
                                        <input type="text" class="form-control form-control-sm" name="telefon" required placeholder="Telefon" value="{{ $branch->telefon }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Whatsapp Telefon</label>
                                        <input type="text" class="form-control form-control-sm" name="whatsapp_telefon" value="{{$branch->whatsapp_telefon}}" placeholder="Whatsapp Telefon">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Fax</label>
                                        <input type="text" class="form-control form-control-sm" name="fax" placeholder="Fax" value="{{ $branch->fax }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Gsm</label>
                                        <input type="text" class="form-control form-control-sm" name="gsm" placeholder="Gsm" value="{{ $branch->gsm }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Eposta</label>
                                        <input type="text" class="form-control form-control-sm" name="email" placeholder="Eposta" value="{{ $branch->email }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Web</label>
                                        <input type="text" class="form-control form-control-sm" name="web" placeholder="Web" value="{{ $branch->web }}" >
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="vergiDairesiHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#vergiDairesi" aria-expanded="true"
                                    aria-controls="vergiDairesi">
                                Vergi Dairesi Bilgileri
                            </button>
                        </h2>
                        <div id="vergiDairesi" class="accordion-collapse collapse show" aria-labelledby="vergiDairesiHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Vergi Dairesi</label>
                                        <input type="text" class="form-control form-control-sm" name="vergi_dairesi" placeholder="Vergi Dairesi" value="{{ $branch->vergi_dairesi }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Vergi No</label>
                                        <input type="text" class="form-control form-control-sm" name="vergi_no" placeholder="Vergi No" value="{{ $branch->vergi_no }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">T.C. Kimlik No</label>
                                        <input type="text" class="form-control form-control-sm" name="tc_no" placeholder="T.C. Kimlik No" value="{{ $branch->tc_no }}" >
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="accordion">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="digerBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#digerBilgiler" aria-expanded="true"
                                    aria-controls="digerBilgiler">
                                Diğer Bilgiler
                            </button>
                        </h2>
                        <div id="digerBilgiler" class="accordion-collapse collapse show" aria-labelledby="digerBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Mersis No</label>
                                        <input type="text" class="form-control form-control-sm" name="mersis_no" placeholder="Mersis No" value="{{ $branch->mersis_no }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Fatura Form Adı</label>
                                        <input type="text" class="form-control form-control-sm" name="fatura_form_adi" placeholder="Fatura Form Adı" value="{{ $branch->fatura_form_adi }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Makbuz Form Adı</label>
                                        <input type="text" class="form-control form-control-sm" name="makbuz_form_adi" placeholder="Makbuz Form Adı" value="{{ $branch->makbuz_form_adi }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Çek Form Adı</label>
                                        <input type="text" class="form-control form-control-sm" name="cek_form_adi" placeholder="Çek Form Adı" value="{{ $branch->cek_form_adi }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Senet Form Adı</label>
                                        <input type="text" class="form-control form-control-sm" name="senet_form_adi" placeholder="Senet Form Adı" value="{{ $branch->senet_form_adi }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">E-Fatura Key</label>
                                        <input type="text" class="form-control form-control-sm" name="efatura_key" placeholder="E-Fatura Key" value="{{ $branch->efatura_key }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Kep Mail</label>
                                        <input type="text" class="form-control form-control-sm" name="kep_mail" value="{{ $branch->kep_mail }}" placeholder="Kep Mail">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">İstek Bekleme Süresi (saniye)</label>
                                        <input type="number" min="0" class="form-control form-control-sm" name="sleep_time" placeholder="İstek Bekleme Süresi (saniye)" value="{{ $branch->sleep_time }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Paynet Bayi Kodu</label>
                                        <input type="text" class="form-control form-control-sm" name="paynet_code" placeholder="Paynet Bayi Kodu" value="{{ $branch->paynet_code }}">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Google SEO Button Bağlantısı</label>
                                        <input type="text" class="form-control form-control-sm" name="google_button"
                                               value="{{ $branch->google_button_1 ? $branch->google_button_1 . ($branch->google_button_2 ? ',' . $branch->google_button_2 : '') : '' }}"
                                               placeholder="Bağlantılar virgülle ayrılarak girilmelidir (https://link1,https://link2)">
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Fren Verileri Cihazdan Geliyor</label>
                                        <select name="brake_values_auto" class="form-control form-control-sm">
                                            <option value="0">Hayır</option>
                                            <option @if($branch->brake_values_auto == 1) selected @endif value="1">Evet</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Dyno Verileri Cihazdan Geliyor</label>
                                        <select name="dyno_values_auto" class="form-control form-control-sm">
                                            <option value="0">Hayır</option>
                                            <option @if($branch->dyno_values_auto == 1) selected @endif value="1">Evet</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kayıtlı IP Adresleri (virgül ile ayırabilirsiniz)</label>
                                        <textarea class="form-control form-control-sm" name="ip_addresses">{{ $branch->ip_addresses }}</textarea>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="firmaLogoHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#firmaLogo" aria-expanded="true"
                                    aria-controls="firmaLogo">
                                Firma Logosu
                            </button>
                        </h2>
                        <div id="firmaLogo" class="accordion-collapse collapse show" aria-labelledby="firmaLogoHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <input type="file" class="dropify" data-height="250" data-default-file="/storage/{{ $branch->logo }}" name="logo"/>
                                </div>
                                <a target="_blank" href="/storage/{{ $branch->logo }}" class="btn btn-secondary btn-sm">Yeni Sekmede Görüntüle</a>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="firmaSozlesmeHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#firmaSozlesme" aria-expanded="true"
                                    aria-controls="firmaSozlesme">
                                Firma Sözleşme
                            </button>
                        </h2>
                        <div id="firmaSozlesme" class="accordion-collapse collapse show" aria-labelledby="firmaSozlesmeHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <input type="file" class="dropify" data-height="250" data-default-file="/storage/{{ $branch->sozlesme }}" name="sozlesme"/>
                                    <a target="_blank" href="/storage/{{ $branch->sozlesme }}" class="btn btn-secondary btn-sm">Yeni Sekmede Görüntüle</a>
                                    <div class="col-12 mt-3">
                                        <label>Sözleşme Başlangıç Tarihi</label>
                                        <input type="date" name="sozlesme_baslangic_tarihi" id="sozlesme_baslangic_tarihi" class="form-control form-control-sm" value="{{ $branch->sozlesme_baslangic_tarihi }}">
                                    </div>
                                    <div class="col-12 mt-3">
                                        <label>Sözleşme Bitiş Tarihi</label>
                                        <input type="date" name="sozlesme_bitis_tarihi" id="sozlesme_bitis_tarihi" value="{{ $branch->sozlesme_bitis_tarihi }}" class="form-control form-control-sm">
                                    </div>

                                    <div class="col-12 mt-3">
                                        <label>Sözleşme Yılı</label>
                                        <input type="number" min="1" name="sozlesme_yili" id="sozlesme_yili" class="form-control form-control-sm" value="{{$branch->sozlesme_yil}}">
                                    </div>


                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="subeKarlilikOranHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#subeKarlilikOran" aria-expanded="true"
                                    aria-controls="subeKarlilikOran">
                                Bayi Karlılık Oranları
                            </button>
                        </h2>
                        <div id="subeKarlilikOran" class="accordion-collapse collapse show" aria-labelledby="subeKarlilikOranHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Bayi Hesabı</label>
                                        <input type="text" class="form-control form-control-sm" name="sube_hesabi" placeholder="Bayi Hesabı" value="{{ $branch->sube_hesabi }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Plus Card Satış O.</label>
                                        <input type="text" class="form-control form-control-sm" name="plus_kart_satis" placeholder="Plus Card Satış O." value="{{ $branch->plus_kart_satis }}" >
                                    </div>
                                    @foreach($tses as $tse)
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">TSE Kodu</label>
                                            <input type="text" class="form-control form-control-sm" name="tse_kodu[]" placeholder="TSE Kodu" value="{{ $tse->tse_kodu }}" >
                                        </div>
                                        <div class="col-xl-6 mt-2">
                                            <label class="form-label">TSE Geçerlilik Tarihi</label>
                                            <input type="date" class="form-control form-control-sm" name="tse_gecerlilik_tarihi[]" placeholder="TSE Geçerlilik Tarihi" value="{{ $tse->gecerlilik_tarihi }}" >
                                        </div>
                                    @endforeach
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">TSE Kodu</label>
                                        <input type="text" class="form-control form-control-sm" name="tse_kodu[]" placeholder="TSE Kodu">
                                    </div>
                                    <div class="col-xl-6 mt-2 tse-tarih">
                                        <label class="form-label">TSE Geçerlilik Tarihi</label>
                                        <input type="date" class="form-control form-control-sm" name="tse_gecerlilik_tarihi[]" placeholder="TSE Geçerlilik Tarihi">
                                    </div>
                                    <button class="btn btn-dark mt-3 btn-sm tse-ekle" type="button">TSE Belgesi Ekle</button>

                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Eksper Merkez O.</label>
                                        <input type="text" class="form-control form-control-sm" name="eksper_merkez" placeholder="Eksper Merkez O." value="{{ $branch->eksper_merkez }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Eksper Oranı</label>
                                        <input type="text" class="form-control form-control-sm" name="eksper_orani" placeholder="Eksper Oranı" value="{{ $branch->eksper_orani }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Hasar Sorgulama</label>
                                        <select name="hasar_sorgulama" class="form-control form-control-sm">
                                            <option @if($branch->hasar_sorgulama == 1) selected @endif value="1">Ototramer Sorgula</option>
                                            <option @if($branch->hasar_sorgulama == 0) selected @endif value="0">Yok</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Plus Card Yükler</label>
                                        <select name="plus_kart_yukle" class="form-control form-control-sm">
                                            <option @if($branch->plus_kart_yukle == 1) selected @endif value="1">Evet</option>
                                            <option @if($branch->plus_kart_yukle == 0) selected @endif value="0">Hayır</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Son Fiyat Değişiklik Tarihi</label>
                                        <input type="date" class="form-control form-control-sm" name="son_fiyat_degisiklik_tarihi" placeholder="Son Fiyat Değişiklik" value="{{ $branch->son_fiyat_degisiklik_tarihi }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Bedelsiz Eksper</label>
                                        <input type="text" class="form-control form-control-sm" name="bedelsiz_eksper" placeholder="Bedelsiz Eksper" value="{{ $branch->bedelsiz_eksper }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Max İndirim Tipi</label>
                                        <select class="form-control form-control-sm iskonto_type" name="iskonto_tip" >
                                            <option @if($branch->iskonto_tip == 1) selected="" @endif  value="1">Miktar</option>
                                            <option @if($branch->iskonto_tip == 2) selected="" @endif value="2">Yüzde</option>
                                        </select>
                                    </div>
                                    <div class="col-xl-6 mt-2 iskonto_tip_percentage @if($branch->iskonto_tip != 2) d-none @endif">
                                        <label class="form-label">Max İndirim Oranı</label>
                                        <input type="text" class="form-control form-control-sm" name="max_indirim_orani" placeholder="Max İndirim Oranı" value="{{ $branch->max_indirim_orani }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2 iskonto_tip_amount @if($branch->iskonto_tip != 1) d-none @endif">
                                        <label class="form-label">Max İndirim Tutarı</label>
                                        <input type="text" class="form-control form-control-sm" name="max_indirim_tutari" placeholder="Max İndirim Tutarı" value="{{ $branch->max_indirim_tutari }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Borç Risk Tutarı</label>
                                        <input type="text" class="form-control form-control-sm" name="borc_risk_tutari" value="{{ $branch->borc_risk_tutari }}" placeholder="Borç Risk Tutarı" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Cari Başı Max. İndirim</label>
                                        <input type="text" class="form-control form-control-sm" name="cari_basi_max_indirim" value="{{ $branch->cari_basi_max_indirim }}" placeholder="Cari Başı Max. İndirim">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="yetkiliBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#yetkiliBilgiler" aria-expanded="true"
                                    aria-controls="yetkiliBilgiler">
                                Yetkili Bilgileri
                            </button>
                        </h2>
                        <div id="yetkiliBilgiler" class="accordion-collapse collapse show" aria-labelledby="yetkiliBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Ad Soyad</label>
                                        <input type="text" class="form-control form-control-sm" name="yetkili_ad_soyad" placeholder="Ad Soyad" value="{{ $branch->yetkili_ad_soyad }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Gsm</label>
                                        <input type="text" class="form-control form-control-sm" name="yetkili_gsm" placeholder="Gsm" value="{{ $branch->yetkili_gsm }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Dahili</label>
                                        <input type="text" class="form-control form-control-sm" name="yetkili_dahili" placeholder="Dahili" value="{{ $branch->yetkili_dahili }}" >
                                    </div>
                                    <div class="col-xl-6 mt-2">
                                        <label class="form-label">Durum</label>
                                        <select name="status" class="form-control form-control-sm">
                                            <option @if($branch->status == 1) selected @endif value="1">Aktif</option>
                                            <option @if($branch->status == 0) selected @endif value="0">Pasif</option>
                                        </select>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12">
                <button class="btn btn-success mb-3">Kaydet</button>
            </div>
        </div>
    </form>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script src="/assets/js/dropify.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script src="/assets/js/imask.js"></script>

    <script>
        if(document.querySelector('input[name="whatsapp_telefon"]')){
            const maskTel = IMask(document.querySelector('input[name="whatsapp_telefon"]'), {
                mask: '(0000) 000 0000'
            });
        }
        var drEvent = $('.dropify').dropify({
            messages: {
                'default': 'Dosya Sürükle veya Tıkla',
                'replace': 'Dosya Sürükle veya Tıkla',
                'remove':  'Kaldır',
                'error':   'Bir Hata Ortaya Çıktı'
            }
        });

        drEvent.on('dropify.beforeClear', function(event, element){
            $('form').append(`<input type='hidden' name='remove_files[]' value='branch/${element.file.name}'/>`)
        });

        $(".select2").select2({
            placeholder: "Ara.."
        });

        $('.ortak-ekle').on('click',function (){
            let html = '<div class="col-xl-4 mt-2"><label class="form-label">Ortak/Yetkili</label>' +
                '<select class="select2" name="partner_id[]"><option value="0">Seçilmedi</option>' +
                @foreach($branch->activeOwners() as $owner)
                    '<option value="{{ $owner->id }}">{{ $owner->fullName() }}</option>' +
                @endforeach
                    '</select></div> <div class="col-xl-4 mt-2"> <label class="form-label">Yüzde(%)</label>' +
                ' <input type="text" class="form-control form-control-sm" name="percent[]" placeholder="Yüzde(%)"></div> <div class="col-xl-4 mt-2">' +
                '<label class="form-label">Hakediş(₺)</label><input type="text" class="form-control form-control-sm" disabled placeholder="0.00₺"></div> '

            $('.ortak-yuzde').after(html)
            $(".select2").select2({
                placeholder: "Ara.."
            });
            $('input[name="percent[]"]').on('keyup',function (){
                sumPartnerPercent()
            })
        })

        $('.tse-ekle').on('click',function (){
            let html = '<div class="col-xl-6 mt-2"><label class="form-label">TSE Kodu</label>' +
                '<input type="text" class="form-control form-control-sm" name="tse_kodu[]" placeholder="TSE Kodu" ></div>' +
                '<div class="col-xl-6 mt-2"><label class="form-label">TSE Geçerlilik Tarihi</label>' +
                '<input type="date" class="form-control form-control-sm" name="tse_gecerlilik_tarihi[]" placeholder="TSE Geçerlilik Tarihi" ></div>'

            $('.tse-tarih').after(html)
        })


        $('input[name="percent[]"]').on('keyup',function (){
            sumPartnerPercent()
        })
        function sumPartnerPercent(){
            let total = 0;
            $('input[name="percent[]"]').each(function (){
                total += parseInt($(this).val())
            })
            if (total > 100){
                alert("Toplam 100'ü Geçiyor! ("+total+")")
            }
        }

        $('select[name="il_kodu"]').on('change',function (){
            let $val = $(this).val()
            $('select[name="ilce_kodu"]').find('option').remove()
            if($val > 0){
                $.ajax({
                    url: "{{ route('api.getTowns') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$val} ,
                    success: function (response) {
                        if (response.items.length > 0){
                            let townID = {{ $branch->ilce_kodu }}
                            $.each(response.items,function (index,item){
                                $('select[name="ilce_kodu"]').append('<option value="'+item.ilce_id+'">'+item.ilce_title+'</option>')
                            })
                            $('select[name="ilce_kodu"] option[value="'+townID+'"]').prop('selected', true);
                            $('select[name="ilce_kodu"]').trigger('change')

                        }
                    }
                });
            }
        })

        addEventListener("DOMContentLoaded", (event) => {
            $('select[name="il_kodu"]').trigger('change')
        });
    </script>

    <script src="/assets/js/html2pdf.bundle.js"></script>
    <script>
        function ExportPdf() {
            // Get the element.
            var element = document.getElementById('divRaporEkrani');
            // Generate the PDF.
            html2pdf().from(element).set({
                margin: 1,
                filename: 'sube_{{ $branch->kod }}.pdf',
                html2canvas: { scale: 2,
                    onclone: function (documentClone) {
                        // Buttonları gizle
                        documentClone.querySelectorAll('button').forEach(function(button){
                            button.style.display = 'none';
                        });
                        // .col-md-6 sınıfı için genişliği ayarla
                        documentClone.querySelectorAll('.col-md-6').forEach(function(col){
                            col.style.width = '100%';
                        });
                    }
                    },
                jsPDF: { orientation: 'portrait', unit: 'cm', format: 'a4', compressPDF: true }
            }).save();
        }

        $('.iskonto_type').change('on',function(){
            var value = $(this).val();
            if(value == 1){
                $('.iskonto_tip_percentage').addClass('d-none')
                $('.iskonto_tip_amount').removeClass('d-none')
            }
            if(value == 2){
                $('.iskonto_tip_percentage').removeClass('d-none')
                $('.iskonto_tip_amount').addClass('d-none')
            }
        })
        $('#sozlesme_yili').change(function(){
            calc_sozlesme(1);
        })
        $('#sozlesme_bitis_tarihi').change(function(){
            calc_sozlesme(2);
        })

        function calc_sozlesme(type){
            var start_date = $('#sozlesme_baslangic_tarihi').val();
            var finish_date = $('#sozlesme_bitis_tarihi').val();
            var sozlesme_yili = $('#sozlesme_yili').val();
            if(type == 1){
                var start_date = $('#sozlesme_baslangic_tarihi').val();
                var sozlesme_yili = parseInt($('#sozlesme_yili').val(), 10);

                var startDate = new Date(start_date);
                console.log(start_date);
                startDate.setFullYear(startDate.getFullYear() + sozlesme_yili);

                var finish_date = startDate.toISOString().split('T')[0];

                $('#sozlesme_bitis_tarihi').val(finish_date);

            }else{
                startDate = new Date(start_date)
                finishDate = new Date(finish_date)
                sozlesme_yili = finishDate.getFullYear() - startDate.getFullYear();
                if(sozlesme_yili > 0){
                    $('#sozlesme_yili').val(sozlesme_yili)
                }

            }
        }
    </script>
@endpush
