@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">

@endpush
@section('title','Stok Ekle')
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('content')
    <form id="mainForm" method="post" action="{{ route('stocks.store') }}" enctype="multipart/form-data">@csrf
        <div class="example">
            <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                <a class="nav-link active" data-bs-toggle="tab" role="tab" href="#stock"
                   aria-selected="true">Stok Kartı Tanımı</a>
                <a class="nav-link" data-bs-toggle="tab" role="tab" href="#prices"
                   aria-selected="false">Stok Fiyatları</a>
                <a class="nav-link" data-bs-toggle="tab" role="tab" href="#mizan"
                   aria-selected="false">Stok Mizan / Muavin</a>
                <a class="nav-link" data-bs-toggle="tab" role="tab" href="#notes"
                   aria-selected="false">Notlar</a>
            </nav>
            <div class="tab-content">
                <div class="tab-pane show active text-muted" id="stock" role="tabpanel">
                    <div class="row">
                        <div class="col-md-12">
                            <div class="accordion" id="accordionPanelsStayOpenExample">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="stokBilgileriHeading">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                                data-bs-target="#stokBilgileri" aria-expanded="true"
                                                aria-controls="stokBilgileri">
                                            Stok Bilgileri
                                        </button>
                                    </h2>
                                    <div id="stokBilgileri" class="accordion-collapse collapse show" aria-labelledby="stokBilgileriHeading">
                                        <div class="accordion-body">
                                            <div class="row">
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label">Cinsi</label>
                                                    <select name="stock_type_id" class="form-control">
                                                        @foreach($stockTypes as $stockType)
                                                            <option data-short="{{ $stockType->short_name }}" value="{{ $stockType->id }}">{{ $stockType->name }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label kod-input">Stok Kodu <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" name="kod" value="{{ \Cache::has('stock_create') ? \Cache::get('stock_create')['kod'] : $stockTypes[0]->short_name }}" required>
                                                </div>
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label ad-input">Stok Adı <span class="text-danger">*</span></label>
                                                    <input type="text" class="form-control" name="ad" value="{{ \Cache::has('stock_create') ? \Cache::get('stock_create')['ad'] : '' }}" placeholder="Adı" required>
                                                </div>
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label birim-input">Birimi</label>
                                                    <select name="stock_unit_id" class="form-control">
                                                        <option value="0">Seçilmedi</option>
                                                        @foreach($stockUnits as $stockUnit)
                                                            <option value="{{ $stockUnit->id }}">{{ $stockUnit->name }}</option>
                                                        @endforeach
                                                    </select>
                                                </div>
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label">KDV (%)</label>
                                                    <select name="kdv" class="form-control">
                                                        <option value="0">%0</option>
                                                        <option value="1">%1</option>
                                                        <option value="8">%8</option>
                                                        <option value="10">%10</option>
                                                        <option value="18">%18</option>
                                                        <option value="20">%20</option>
                                                    </select>
                                                </div>
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label grup-input">Grubu</label>
                                                    <select name="stock_group_id" class="form-control">
                                                        <option value="0">Seçilmedi</option>
                                                    </select>
                                                </div>
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label miktar-input">Stok Miktarı <span class="text-danger">*</span></label>
                                                    <input type="number" class="form-control" name="miktar" value="{{ \Cache::has('stock_create') ? \Cache::get('stock_create')['miktar'] : '' }}" placeholder="Stok Miktarı" required>
                                                </div>
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label">Gösterim Sırası </label>
                                                    <input type="number" class="form-control" name="rank" value="{{ \Cache::has('stock_create') ? \Cache::get('stock_create')['rank'] : '' }}" placeholder="Gösterim Sırası">
                                                </div>
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label">İndirim Uygula</label>
                                                    <select name="indirim_uygula" class="form-control">
                                                        <option value="1">Evet</option>
                                                        <option value="0">Hayır</option>
                                                    </select>
                                                </div>
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label">Durum</label>
                                                    <select name="status" class="form-control">
                                                        <option value="1">Aktif</option>
                                                        <option value="0">Pasif</option>
                                                    </select>
                                                </div>
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label">Eksperde Göster</label>
                                                    <select name="show_on_expertise" class="form-control">
                                                        <option value="1">Evet</option>
                                                        <option value="0">Hayır</option>
                                                    </select>
                                                </div>
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label">Yol Yardımı</label>
                                                    <select name="yol_yardimi" class="form-control">
                                                        <option value="1">Evet</option>
                                                        <option value="0">Hayır</option>
                                                    </select>
                                                </div>
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label">Sorgu Hizmeti</label>
                                                    <select name="sorgu_hizmeti" class="form-control">
                                                        <option value="1">Evet</option>
                                                        <option value="0">Hayır</option>
                                                    </select>
                                                </div>
                                                <div class="col-xl-4 mt-2">
                                                    <label class="form-label" for="iskonto">İskonto Tutarı</label>
                                                    <input type="text" class="form-control" name="iskonto_tutari">
                                                </div>
                                                <div class="col-12"></div>
                                                <div class="col-md-2 mt-3">
                                                    <label class="form-label" for="car">Araç</label>
                                                    <input type="checkbox" class="form-check-input" id="car"  name="car" style="border: 1px solid #0e0e0e">
                                                </div>
                                                <div class="col-md-2 mt-3">
                                                    <label class="form-label" for="brake">Fren</label>
                                                    <input type="checkbox" class="form-check-input" id="brake" name="brake" style="border: 1px solid #0e0e0e">
                                                </div>
                                                <div class="col-md-2 mt-3">
                                                    <label class="form-label" for="bodywork">Kaporta</label>
                                                    <input type="checkbox" class="form-check-input" id="bodywork" name="bodywork" style="border: 1px solid #0e0e0e">
                                                </div>
                                                <div class="col-md-2 mt-3">
                                                    <label class="form-label" for="diagnostic">Diagnostic</label>
                                                    <input type="checkbox" class="form-check-input" id="diagnostic" name="diagnostic" style="border: 1px solid #0e0e0e">
                                                </div>
                                                <div class="col-md-2 mt-3">
                                                    <label class="form-label" for="internal_control">İç Kontroller</label>
                                                    <input type="checkbox" class="form-check-input" id="internal_control" name="internal_control" style="border: 1px solid #0e0e0e">
                                                </div>
                                                <div class="col-md-2 mt-3">
                                                    <label class="form-label" for="tire_and_rim">Lastik ve Jant</label>
                                                    <input type="checkbox" class="form-check-input" id="tire_and_rim" name="tire_and_rim" style="border: 1px solid #0e0e0e">
                                                </div>
                                                <div class="col-md-2 mt-3">
                                                    <label class="form-label" for="sub_control_and_engine">Alt Kontroller - Motor</label>
                                                    <input type="checkbox" class="form-check-input" id="sub_control_and_engine" name="sub_control_and_engine" style="border: 1px solid #0e0e0e">
                                                </div>
                                                <div class="col-md-2 mt-3">
                                                    <label class="form-label" for="component">Komponent</label>
                                                    <input type="checkbox" class="form-check-input" id="component" name="component" style="border: 1px solid #0e0e0e">
                                                </div>
                                                <div class="col-md-2 mt-3">
                                                    <label class="form-label" for="co2">Co2 Kaçak Testi</label>
                                                    <input type="checkbox" class="form-check-input" id="co2" name="co2" style="border: 1px solid #0e0e0e">
                                                </div>

                                                <label for="" class="mt-4 form-label">
                                                    Ödeme Yöntemleri
                                                </label>

                                                <div class="col-md-2 mt-3">
                                                    <label class="form-label" for="payment_type">Normal Ödeme</label>
                                                    <input type="checkbox" class="form-check-input" id="payment_type" name="payment_type[]" checked="" value="1" style="border: 1px solid #0e0e0e">
                                                </div>
                                                <div class="col-md-2 mt-3">
                                                    <label class="form-label" for="payment_type_plus_card">Plus Card</label>
                                                    <input type="checkbox" class="form-check-input" id="payment_type_plus_card" checked="" name="payment_type[]" value="2" style="border: 1px solid #0e0e0e">
                                                </div>
                                                <div class="col-md-2 mt-3">
                                                    <label class="form-label" for="payment_type_contracts">Sözleşme</label>
                                                    <input type="checkbox" class="form-check-input" id="payment_type_contracts" checked="" name="payment_type[]" value="3" style="border: 1px solid #0e0e0e">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="accordion" id="accordionPanelsStayOpenExample">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="alisMuhasebeHeading">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                                data-bs-target="#alisMuhasebe" aria-expanded="true"
                                                aria-controls="alisMuhasebe">
                                            Alış Muhasebe Kodları
                                        </button>
                                    </h2>
                                    <div id="alisMuhasebe" class="accordion-collapse collapse show" aria-labelledby="alisMuhasebeHeading">
                                        <div class="accordion-body">
                                            <div class="row">
                                                <div class="col-xl-12 mt-2">
                                                    <label class="form-label">Fatura</label>
                                                    <input type="text" class="form-control" name="muhasebe_alis_fatura_kodu" value="{{ \Cache::has('stock_create') ? \Cache::get('stock_create')['muhasebe_alis_fatura_kodu'] : '' }}" placeholder="Fatura">
                                                </div>
                                                <div class="col-xl-12 mt-2">
                                                    <label class="form-label">İade</label>
                                                    <input type="text" class="form-control" name="muhasebe_alis_iade_kodu" value="{{ \Cache::has('stock_create') ? \Cache::get('stock_create')['muhasebe_alis_iade_kodu'] : '' }}" placeholder="İade">
                                                </div>
                                                <div class="col-xl-12 mt-2">
                                                    <label class="form-label">İskonto</label>
                                                    <input type="text" class="form-control" name="muhasebe_alis_iskonto_kodu" value="{{ \Cache::has('stock_create') ? \Cache::get('stock_create')['muhasebe_alis_iskonto_kodu'] : '' }}" placeholder="İskonto">
                                                </div>
                                                <div class="col-xl-6 mt-2">
                                                    <label class="form-label">Fark Fatura</label>
                                                    <input type="text" class="form-control" name="muhasebe_alis_fark_fatura_kodu" value="{{ \Cache::has('stock_create') ? \Cache::get('stock_create')['muhasebe_alis_fark_fatura_kodu'] : '' }}" placeholder="Fark Fatura">
                                                </div>
                                                <div class="col-xl-6 mt-2">
                                                    <label class="form-label">Maliyet</label>
                                                    <input type="text" class="form-control" name="muhasebe_alis_maliyet_kodu" value="{{ \Cache::has('stock_create') ? \Cache::get('stock_create')['muhasebe_alis_maliyet_kodu'] : '' }}" placeholder="Maliyet">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="accordion" id="accordionPanelsStayOpenExample">
                                <div class="accordion-item">
                                    <h2 class="accordion-header" id="satisMuhasebeHeading">
                                        <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                                data-bs-target="#satisMuhasebe" aria-expanded="true"
                                                aria-controls="satisMuhasebe">
                                            Satış Muhasebe Kodları
                                        </button>
                                    </h2>
                                    <div id="satisMuhasebe" class="accordion-collapse collapse show" aria-labelledby="satisMuhasebeHeading">
                                        <div class="accordion-body">
                                            <div class="row">
                                                <div class="col-xl-12 mt-2">
                                                    <label class="form-label">Fatura</label>
                                                    <input type="text" class="form-control" name="muhasebe_satis_fatura_kodu" value="{{ \Cache::has('stock_create') ? \Cache::get('stock_create')['muhasebe_satis_fatura_kodu'] : '' }}" placeholder="Fatura">
                                                </div>
                                                <div class="col-xl-12 mt-2">
                                                    <label class="form-label">İade</label>
                                                    <input type="text" class="form-control" name="muhasebe_satis_iade_kodu" value="{{ \Cache::has('stock_create') ? \Cache::get('stock_create')['muhasebe_satis_iade_kodu'] : '' }}" placeholder="İade">
                                                </div>
                                                <div class="col-xl-12 mt-2">
                                                    <label class="form-label">İskonto</label>
                                                    <input type="text" class="form-control" name="muhasebe_satis_iskonto_kodu" value="{{ \Cache::has('stock_create') ? \Cache::get('stock_create')['muhasebe_satis_iskonto_kodu'] : '' }}" placeholder="İskonto">
                                                </div>
                                                <div class="col-xl-6 mt-2">
                                                    <label class="form-label">Fark Fatura</label>
                                                    <input type="text" class="form-control" name="muhasebe_satis_fark_fatura_kodu" value="{{ \Cache::has('stock_create') ? \Cache::get('stock_create')['muhasebe_satis_fark_fatura_kodu'] : '' }}" placeholder="Fark Fatura">
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane text-muted" id="prices" role="tabpanel">
                    <div class="accordion" id="accordionPanelsStayOpenExample">
                        <div class="accordion-item">
                            <h2 class="accordion-header" id="fiyatlarHeading">
                                <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                        data-bs-target="#fiyatlar" aria-expanded="true"
                                        aria-controls="fiyatlar">
                                    Fiyatlar
                                </button>
                            </h2>
                            <div id="fiyatlar" class="accordion-collapse collapse show" aria-labelledby="fiyatlarHeading">
                                <div class="accordion-body">
                                    <div class="row">
                                        <div class="col-12">
                                            <div class="row">
                                                <div class="col-md-3 mt-3">
                                                    <label class="form-label">Kampanya Adı</label>
                                                    <select name="new_campaing"  id="new_campaing"  class="select2">
                                                        <option value="0">Seçim Yapınız </option>
                                                        @foreach($campaigns as $campaign)
                                                            <option data-campaingName = "{{ $campaign->name }}" value="{{$campaign->id}}">{{$campaign->name}}</option>
                                                        @endforeach
                                                    </select>
                                                </div>

                                                <div class="col-md-2 mt-3">
                                                    <label class="form-label">Bayi Kodu</label>
                                                    <input type="text" class="form-control new_sube_kodu" name="new_sube_kodu" placeholder="Bayi Kodu">
                                                </div>

                                                <div class="col-md-3 mt-3">
                                                    <label class="form-label">KDV Hariç Fiyat</label>
                                                    <input type="text" class="form-control new_kdv_haric_fiyat"  name="new_kdv_haric_fiyat" placeholder="KDV Hariç Fiyat">
                                                </div>

                                                <div class="col-md-3 mt-3">
                                                    <label class="form-label">KDV Dahil Fiyat</label>
                                                    <input type="text" class="form-control new_kdv_dahil_fiyat" name="new_kdv_dahil_fiyat"  placeholder="KDV Dahil Fiyat">
                                                </div>

                                                <div class="col-md-1 mt-3 d-flex flex-column justify-content-center align-items-start">
                                                    <button type="button" class="btn btn-success campaing_add mt-4">
                                                        <i class="fa fa-plus"></i>
                                                    </button>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">Standart</label>
                                            <input type="text" class="form-control" disabled placeholder="Standart">
                                        </div>
                                        <div class="col-md-2">
                                            <label class="form-label">Bayi Kodu</label>
                                            <input type="text" class="form-control" name="sube_kodu[0]" placeholder="Bayi Kodu">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">KDV Hariç Fiyat</label>
                                            <input type="text" class="form-control kdv_haric_fiyat" data-index="0" name="kdv_haric_fiyat[0]" placeholder="KDV Hariç Fiyat">
                                        </div>
                                        <div class="col-md-3">
                                            <label class="form-label">KDV Dahil Fiyat</label>
                                            <input type="text" class="form-control kdv_dahil_fiyat" data-index="0" name="kdv_dahil_fiyat[0]"  placeholder="KDV Dahil Fiyat">
                                        </div>
                                        <div class="col-12">
                                            <div class="row add_new_campaing">
                                            </div>
                                        </div>

                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="tab-pane text-muted" id="mizan" role="tabpanel"></div>
                <div class="tab-pane text-muted" id="notes" role="tabpanel">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="notesHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#notes" aria-expanded="true"
                                    aria-controls="notes">
                                Notlar
                            </button>
                        </h2>
                        <div id="notes" class="accordion-collapse collapse show" aria-labelledby="notesHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    @for($i = 0;$i < 10;$i++)
                                        <div class="col-md-6 mt-3">
                                            <label class="form-label">Açıklama</label>
                                            <input type="text" class="form-control" name="aciklama[{{ $i }}]" placeholder="Açıklama">
                                        </div>
                                        <div class="col-md-3 mt-3">
                                            <label class="form-label">Uyarı Tarihi</label>
                                            <input type="date" class="form-control" name="uyari_tarihi[{{ $i }}]" placeholder="Uyarı Tarihi">
                                        </div>
                                        <div class="col-md-3 mt-3">
                                            <label class="form-label">Kayıt Tarihi</label>
                                            <input type="date" class="form-control" name="kayit_tarihi[{{ $i }}]" placeholder="Kayıt Tarihi">
                                        </div>
                                    @endfor
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12 text-center mt-2">
                    <button class="btn btn-success mb-3">Kaydet</button>
                </div>
            </div>
        </div>
        <input type="hidden" name="session_name" value="stock_create">
    </form>
    @if(\Illuminate\Support\Facades\Cache::has('stock_create'))
        <div class="modal fade" id="deleteCacheModal" tabindex="-1"
             aria-labelledby="deleteCacheModal" data-bs-keyboard="false"
             aria-hidden="true">
            <!-- Scrollable modal -->
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title" id="staticBackdropLabel2">Mevcut Kayıt Bulundu!
                        </h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Önceden oluşturduğunuz kayıt kapanmadı!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success"
                                data-bs-dismiss="modal">Kayıta Devam Et</button>
                        <button type="submit" form="deleteCache" class="btn btn-danger">Yeni Kayıt Oluştur</button>
                        <form id="deleteCache" method="post" action="{{ route('deleteCache') }}">@csrf<input type="hidden" name="type" value="stock_create"></form>
                    </div>
                </div>
            </div>
        </div>
    @endif
@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/js/select2.min.js"></script>


    <script>

        $(".select2").select2({
            placeholder: "Ara.."
        });
        addEventListener("DOMContentLoaded", (event) => {
            @if(\Illuminate\Support\Facades\Cache::has('stock_create'))
            $('#deleteCacheModal').modal('toggle')
            @endif
        });
        $('select[name="stock_type_id"]').on('change',function (){
            $('.ad-input').html($( "select[name='stock_type_id'] option:selected" ).text() + ' Adı')
            $('.kod-input').html($( "select[name='stock_type_id'] option:selected" ).text() + ' Kodu')
            $('.grup-input').html($( "select[name='stock_type_id'] option:selected" ).text() + ' Grubu')
            $('.miktar-input').html($( "select[name='stock_type_id'] option:selected" ).text() + ' Miktarı')
            $('.birim-input').html($( "select[name='stock_type_id'] option:selected" ).text() + ' Birimi')
        })

        $(document).ready(function (){
            $('select[name="stock_type_id"]').trigger('change')
        })

        $('select[name="stock_type_id"]').on('change',function (){
            $('select[name="stock_group_id"]').find('option').remove().end().append('<option value="0">Seçilmedi</option>')
            if($(this).val() != 0){
                $('input[name="kod"]').val($("select[name=stock_type_id] option:selected").data('short'))
                $.ajax({
                    url: "{{ route('api.getStockTypeGroups') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','id':$('select[name="stock_type_id"]').val()} ,
                    success: function (response) {
                        $.each(response.items,function (index,item){
                            $('select[name="stock_group_id"]').append('<option value="'+item.id+'">'+item.name+'</option>')
                        })
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }
        })

        setInterval(function (){
            $.ajax({
                url: "{{ route('saveToSession') }}",
                type: "post",
                data: $('#mainForm').serialize(),
                success: function (response) {

                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        },30 * 1000)

        $(document).ready(function() {
            // kdv_haric_fiyat inputları değiştiğinde tetiklenecek fonksiyon
            $(".kdv_haric_fiyat").change(function() {

                // Değişen inputun index'ini al
                const index = $(this).data('index');
                console.log(index)
                // Seçilen KDV oranını al
                const kdvOrani = parseFloat($("select[name='kdv']").val());

                // KDV dahil fiyatı hesapla
                const kdvDahilFiyat = parseFloat($(this).val()) * (1 + (kdvOrani/100));

                if(!isNaN(kdvDahilFiyat.toFixed(2)))
                {
                    // Hesaplanan değeri kdv_dahil_fiyat inputuna yaz
                    $(".kdv_dahil_fiyat").eq(index).val(kdvDahilFiyat.toFixed(2));
                }

            });
            // kdv_haric_fiyat inputları değiştiğinde tetiklenecek fonksiyon
            $(".kdv_dahil_fiyat").change(function() {

                // Değişen inputun index'ini al
                const index = $(this).data('index');
                console.log(index)
                // Seçilen KDV oranını al
                const kdvOrani = parseFloat($("select[name='kdv']").val());

                const kdvDahilFiyat = parseFloat($(this).val());

                // KDV dahil fiyatı hesapla
                const kdvMiktari = kdvDahilFiyat / (1 + kdvOrani/100) * (kdvOrani/100);

                console.log(kdvDahilFiyat,kdvOrani,kdvMiktari)
                // Hesaplanan değeri kdv_dahil_fiyat inputuna yaz
                if(!isNaN((kdvDahilFiyat - kdvMiktari.toFixed(2)))){
                    kdvHaricFiyat = kdvDahilFiyat - kdvMiktari.toFixed(2)
                    $(".kdv_haric_fiyat").eq(index).val(kdvHaricFiyat.toFixed(2));
                }

            });


            $(".new_kdv_haric_fiyat").change(function() {

                // Değişen inputun index'ini al
                const index = $(this).data('index');
                // Seçilen KDV oranını al
                const kdvOrani = parseFloat($("select[name='kdv']").val());

                // KDV dahil fiyatı hesapla
                const kdvDahilFiyat = parseFloat($(this).val()) * (1 + (kdvOrani/100));

                if(!isNaN(kdvDahilFiyat.toFixed(2)))
                {
                    // Hesaplanan değeri kdv_dahil_fiyat inputuna yaz
                    $(".new_kdv_dahil_fiyat").val(kdvDahilFiyat.toFixed(2));
                }

            });
            // kdv_haric_fiyat inputları değiştiğinde tetiklenecek fonksiyon
            $(".new_kdv_dahil_fiyat").change(function() {

                // Değişen inputun index'ini al
                const index = $(this).data('index');
                // Seçilen KDV oranını al
                const kdvOrani = parseFloat($("select[name='kdv']").val());

                const kdvDahilFiyat = parseFloat($(this).val());

                // KDV dahil fiyatı hesapla
                const kdvMiktari = kdvDahilFiyat / (1 + kdvOrani/100) * (kdvOrani/100);

                console.log(kdvDahilFiyat,kdvOrani,kdvMiktari)
                // Hesaplanan değeri kdv_dahil_fiyat inputuna yaz
                if(!isNaN((kdvDahilFiyat - kdvMiktari.toFixed(2)))){
                    kdvHaricFiyat = kdvDahilFiyat - kdvMiktari.toFixed(2)
                    $(".new_kdv_haric_fiyat").val(kdvHaricFiyat.toFixed(2));
                }

            });

            $('.campaing_add').click(function(){
                var id = $('#new_campaing').val();
                if(id != '' && id >0){
                    var name = $('select#new_campaing option:selected').data('campaingname');
                    var new_sube_kod = $('.new_sube_kodu').val()
                    var new_kdv_haric_fiyat = $('.new_kdv_haric_fiyat').val()
                    var new_kdv_dahil_fiyat = $('.new_kdv_dahil_fiyat').val()
                    var html = '<div class="col-12 new_campaing_code'+id+'"><div class="row">' +
                        '<div class="col-md-3 mt-3">' +
                        '<label>'+name+'</label><input type="text" disabled class="form-control" placeholder="'+name+'">' +
                        '</div>' +
                        '<div class="col-md-2 mt-3">' +
                        '<label>Bayi Kodu</label><input type="text" value="'+new_sube_kod+'" name="sube_kodu['+id+']"  class="form-control" placeholder="Bayi Kodu">' +
                        '</div>' +
                        '<div class="col-md-3 mt-3">' +
                        '<label>KDV Hariç Fiyat</label><input type="text" value="'+new_kdv_haric_fiyat+'" name="kdv_haric_fiyat['+id+']"  class="form-control kdv_haric_fiyat" placeholder="KDV Hariç Fiyat">' +
                        '</div>' +
                        '<div class="col-md-3 mt-3">' +
                        '<label>KDV Dahil Fiyat</label><input type="text" value="'+new_kdv_dahil_fiyat+'" name="kdv_dahil_fiyat['+id+']"  class="form-control kdv_dahil_fiyat" placeholder="KDV Hariç Fiyat">' +
                        '</div>' +
                        '<div class="col-md-1 mt-3 d-flex flex-column justify-content-center align-items-start">' +
                        '<button type="button" data-id="'+id+'" class="btn btn-danger mt-4 new_delete_campaing"><i class="fa fa-trash"></i></button>' +
                        '</div>' +
                        '</div>'

                    $('.add_new_campaing').append(html)
                    $('.new_sube_kodu').val('')
                    $('.new_kdv_haric_fiyat').val('')
                    $('.new_kdv_dahil_fiyat').val('')
                    $('select#new_campaign').val('').trigger('change.select2');
                    $('select#new_campaing option:selected').attr('disabled', '')
                    $('.new_delete_campaing').click(function(){
                        var id = $(this).attr('data-id')
                        $('select#new_campaing option[value="' + id + '"]').removeAttr('disabled', '')

                        $('.new_campaing_code'+id).remove();
                    })
                }


            })

        });
    </script>
@endpush
