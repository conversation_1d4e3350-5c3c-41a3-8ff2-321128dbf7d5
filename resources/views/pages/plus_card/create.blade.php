@extends('pages.build')
@push('css')
    <link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@push('message') <span class="text-danger">(Kırmızı Kenarlı Alanlar Zorunludur!)</span> @endpush
@section('title','Plus Card Ekle')
@section('content')
    <form id="mainForm" method="post" action="{{ route('plus-cards.store') }}">@csrf
        <div class="row">
            <div class="col-md-4">
                <div class="accordion" id="accordionPanelsStayOpenExample">
                    <div class="accordion-item">
                        <h2 class="accordion-header" id="temelBilgilerHeading">
                            <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                    data-bs-target="#temelBilgiler" aria-expanded="true"
                                    aria-controls="temelBilgiler">
                                Kart <PERSON>giler<PERSON>
                            </button>
                        </h2>
                        <div id="temelBilgiler" class="accordion-collapse collapse show" aria-labelledby="temelBilgilerHeading">
                            <div class="accordion-body">
                                <div class="row">
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Kart Numarası<span class="text-danger">*</span></label>
                                        <input type="text" class="form-control" name="no" placeholder="Kart Numarası" value="{{ \Cache::has('plus_card_create') ? \Cache::get('plus_card_create')['no'] : '' }}" required>
                                        <label>Toplu Ekle</label>
                                        <input type="checkbox" class="mt-2" name="multiple">
                                    </div>
                                    <div class="col-12 d-none multi">
                                        <label>Eklenecek Adet</label>
                                        <input type="number" class="form-control" value="{{ \Cache::has('plus_card_create') ? \Cache::get('plus_card_create')['count'] : 1}}" min="1" name="count">
                                    </div>
                                    <div class="col-xl-12">
                                        <div class="form-group mt-2">
                                            <label class="form-label">Bayi<span class="text-danger">*</span></label>
                                            <input type="hidden" name="branch_id">
                                            <input type="text" class="form-control search-branch" required placeholder="Aramak için yazınız (En az 3 kelime)">
                                            <div class="filter-results find-branch"></div>
                                        </div>
                                    </div>
                                    <div class="col-xl-12">
                                        <div class="row">
                                            <div class="col-12">
                                                <label class="form-label">Cari</label>
                                            </div>
                                            <div class="form-group col-10 mt-2">
                                                <div class="d-flex">
                                                    <div class="form-check">
                                                        <input class="form-check-input" type="radio" name="customer_search_type"
                                                               value="cep" id="saticiSearchTypeGsm">
                                                        <label class="form-check-label" for="saticiSearchTypeGsm">
                                                            GSM
                                                        </label>
                                                    </div>
                                                    <div class="form-check mx-3">
                                                        <input class="form-check-input" type="radio" name="customer_search_type"
                                                               value="cari_kod" id="saticiSearchTypePlusCard">
                                                        <label class="form-check-label" for="saticiSearchTypePlusCard">
                                                            Yeni Cari Kod
                                                        </label>
                                                    </div>
                                                    <div class="form-check">
                                                        <input class="form-check-input" checked type="radio"
                                                               name="customer_search_type" value="unvan" id="saticiSearchTypeUnvan">
                                                        <label class="form-check-label" for="saticiSearchTypeUnvan">
                                                            Unvan
                                                        </label>
                                                    </div>
                                                </div>
                                                <input type="hidden" name="customer_id">
                                                <input type="text" class="form-control search-customer" placeholder="Aramak için yazınız (En az 3 kelime)" @if($authUser->type != "admin") required @endif>
                                                <div class="filter-results find-customer"></div>
                                            </div>
                                            <div class="col-2 mt-3">
                                                <button type="button" class="btn  btn-sm btn-primary customer-search mt-3">Ara</button>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="col-xl-12 mt-2">
                                        <label class="form-label">Son Geçerlilik Tarihi</label>
                                        <select name="valid_date_if" class="form-control mb-2" id="valid_date_if" >
                                            @if($authUser->type == "admin") <option value="0">Son Geçerlilik Tarihi Uygulanmasın</option> @endif
                                            <option value="1">Son Geçerlilik Tarihi Uygulansın</option>
                                        </select>
                                        <div class="valid_date_input" @if($authUser->type == "admin") style="display: none" @endif>
                                            <input type="date" class="form-control valid_date_input" name="valid_date" value="{{ \Cache::has('plus_card_create') ? \Cache::get('plus_card_create')['valid_date'] : now()->addYear()->format('Y-m-d') }}">
                                        </div>
                                    </div>


                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12 mt-2">
                    <button class="btn btn-success mb-3 saveButton">Kaydet</button>
                </div>
            </div>

        </div>
        <input type="hidden" name="session_name" value="plus_card_create">
    </form>
    @if(\Illuminate\Support\Facades\Cache::has('plus_card_create'))
        <div class="modal fade" id="deleteCacheModal" tabindex="-1"
             aria-labelledby="deleteCacheModal" data-bs-keyboard="false"
             aria-hidden="true">
            <!-- Scrollable modal -->
            <div class="modal-dialog modal-dialog-centered">
                <div class="modal-content">
                    <div class="modal-header">
                        <h6 class="modal-title" id="staticBackdropLabel2">Mevcut Kayıt Bulundu!
                        </h6>
                        <button type="button" class="btn-close" data-bs-dismiss="modal"
                                aria-label="Close"></button>
                    </div>
                    <div class="modal-body">
                        <p>Önceden oluşturduğunuz kayıt kapanmadı!</p>
                    </div>
                    <div class="modal-footer">
                        <button type="button" class="btn btn-success"
                                data-bs-dismiss="modal">Kayıta Devam Et</button>
                        <button type="submit" form="deleteCache" class="btn btn-danger">Yeni Kayıt Oluştur</button>
                        <form id="deleteCache" method="post" action="{{ route('deleteCache') }}">@csrf<input type="hidden" name="type" value="plus_card_create"></form>
                    </div>
                </div>
            </div>
        </div>
    @endif
    <div class="modal fade" id="searchCustomerModal" tabindex="-1"
         aria-labelledby="searchCustomerModal" data-bs-keyboard="false"
         aria-hidden="true">
        <div class="modal-dialog modal-lg modal-dialog-centered">
            <div class="modal-content">
                <div class="modal-header">
                    <h6 class="modal-title" id="staticBackdropLabel2">Bulunan Sonuçlar
                    </h6>
                    <button type="button" class="btn-close" data-bs-dismiss="modal"
                            aria-label="Close"></button>
                </div>
                <div class="modal-body" style="max-height: 300px;overflow: auto; padding-top: 0px;">
                    <table class="table table-striped table-responsive">
                        <thead style="position: sticky;top: 0">
                        <tr>
                            <th>Hesap Kodu</th>
                            <th>Ad Soyad</th>
                            <th>Bayi</th>
                            <th>Telefon</th>
                            <th>T.C/Vergi No</th>
                            <th>Plus Card</th>
                        </tr>
                        </thead>
                        <tbody class="filter-results-new">

                        </tbody>
                    </table>
                    <table id="header-fixed"></table>
                </div>
                <div class="modal-footer cari">

                </div>
            </div>
        </div>
    </div>

@endsection
@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')
    <script src="/assets/js/select2.min.js"></script>
    <script>
        // check the card number
        function isValidCardNumber(cardNumber) {
            return /^04445417\d{4}\d{4}$/.test(cardNumber.replace(/\s/g, '')); // Validate format test() returns true or false
        }

        // Auto-format card number input (add spaces after every 4 digits)
        $('input[name="no"]').on('input', function() {
            let value = $(this).val().replace(/\D/g, ''); // Remove non-digits
            let formattedValue = value.replace(/(.{4})/g, '$1 ').trim(); // Add space after every 4 digits
            $(this).val(formattedValue);
        });

        // Validate when clicking "Kaydet" button
        $('.saveButton').on('click', function (event) {
            // Check for valid card number and ID format
            let cardNumberInput = $('input[name="no"]');
            let cardNumber = cardNumberInput.val().trim();

            if (!isValidCardNumber(cardNumber)) {
                event.preventDefault(); // Stop form submission

                Swal.fire({
                    title: "Geçersiz Kart Numarası!",
                    text: "Kart numarası 0444 5417 XXXX XXXX formatında olmalıdır. Lütfen doğru formatta giriniz.",
                    icon: "warning",
                    confirmButtonText: "Tamam"
                }).then(() => {
                    cardNumberInput.focus(); // Focus on the input field
                });

                return false; //  Stop further execution
            }
            // If valid, allow form submission
        });

        $(".select2").select2({
            placeholder: "Ara.."
        });

        $('#valid_date_if').change(function () {
            var value = $('#valid_date_if').val();

            if (value == 1) {
                $('.valid_date_input').show();
            } else {
                $('.valid_date_input').hide();
            }
        });

        function stock_price_calc(){
            var price = $("select[name=new_stock_new] option:selected").data('price')
            var credi = $('#new_credi').val();
            var result = parseInt(price) * parseInt(credi);
            result = result.toLocaleString('tr-TR');
            if(!isNaN(result)){
                $('.total_price_new').html(result.toLocaleString('tr-TR') + '₺')
            }else{
                $('.total_price_new').html('0₺')
            }

        }
        function stock_price_calc_2(id){
            var price = $("#stocks_"+id+" option[value='" + id + "']").data('price')
            var credi = $('#stock_credi_'+id).val();
            console.log(price,credi)
            var result = parseInt(price) * parseInt(credi);
            result = result.toLocaleString('tr-TR');
            if(!isNaN(result)){
                $('.total_price_'+id).html(result.toLocaleString('tr-TR') + '₺')
            }else{
                $('.total_price_'+id).html('0₺')
            }

        }

        addEventListener("DOMContentLoaded", (event) => {
            @if(\Illuminate\Support\Facades\Cache::has('plus_card_create'))
            $('#deleteCacheModal').modal('toggle')
            @endif
        });
        $('input[name="multiple"]').on('click',function (){
            if ($(this)[0].checked == true){
                $('.multi').removeClass('d-none')
            }else{
                $('.multi').addClass('d-none')
            }
        })

        $('.search-branch').on('keyup',function (){
            let $val = $(this).val();
            if ($(this).val().length > 2){
                $.ajax({
                    url: "{{ route('api.getBranchesForPlusCard') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','search':$val} ,
                    success: function (response) {
                        $('.find-branch').css('display','block')
                        if (response.items.length > 0){
                            let html = ''
                            $.each(response.items,function (index,item){
                                html += '<div class="filter-item" data-id="'+item.id+'" data-unvan="'+item.unvan+'">'+item.unvan+'</div>'
                            })
                            $('.find-branch').html(html)

                            $('.find-branch .filter-item').on('click',function (){
                                $('input[name=branch_id]').val($(this).data('id'))
                                $('.find-branch').css('display','none')
                                $('.search-branch').val($(this).data('unvan'))
                                $('.search-branch').removeAttr('required')
                            })

                        }else{
                            $('.find-branch').html("Hiçbir Kayıt Bulunamadı! <a class='text-danger' href='{{ route('branches.create') }}'>Yeni Kayıt Ekle</a>")
                        }
                    }
                });
            }else{
                $('.find-branch').css('display','none')
            }
        })
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }
        $('.customer-search').click(function(){
            $('.customer-search').attr('disabled','disabled');
            let $val = $('.search-customer').val();
            $.ajax({
                url: "{{ route('api.getCustomersForPlusCard') }}",
                type: "post",
                data: {'_token':'{{ csrf_token() }}','search':$val,'search_type':$('input[name="customer_search_type"]:checked').val()} ,
                success: function (response) {

                    if (response.items.length > 0) {

                        let html = ""
                        $.each(response.items, function (index, item) {
                            let tcVergiNo = item.vergi_no || item.tc_no;
                            let cep = item.cep;
                            let mask;
                            let mask2;
                            if (tcVergiNo) {
                                let first4 = tcVergiNo.substring(0, 4);
                                let tcVergi = tcVergiNo.substring(4, tcVergiNo.length - 4).replace(/\d/g, "*");
                                mask = first4 + tcVergi
                            }
                            if (cep) {
                                let first4x = cep.substring(0, 2);
                                let last5x = cep.substring(cep.length - 4);

                                let stars = cep.substring(0, cep.length - 4).replace(/\d/g, "*");
                                mask2 = stars + last5x
                            }

                            html += '<tr class="filter-item" ' +
                                'data-id="' + item.id + '" ' +
                                'data-unvan="' + item.unvan + '" ' +
                                'data-telefon="' + (item.telefon ? item.telefon : item.cep) + '" ' +
                                'data-kod="' + item.cari_kod + '" ' +
                                'data-tc="' + item.tc + '">'
                            html += '<td>' + (item.cari_kod || '') + '</td>'
                            html += '<td>' + item.unvan + '</td>'
                            html += '<td>' + item.branch_name + '</td>'
                            html += '<td>' + (mask2 || '') + '</td>'
                            html += '<td>' + (mask || '') + '</td>'
                            html += '<td>' + (item.plusCardNo || '') + '</td>'
                            html += '</tr>'
                        })
                        $('.filter-results-new').html(html)
                        let addCustomerHtml = "<a style=\'font-size:.9rem\' class=\"btn btn-sm btn-danger open-add-customer-ruhsat-page\" href=\"{{ route('customers.create',['customer'=>'ruhsat','return_url'=>url()->current(),'type'=>'bireysel']) }}&unvan=" + $('.search-customer').val() + "\">Yeni Bireysel Kayıt</a><br>"
                        addCustomerHtml += "<a style=\'font-size:.9rem\' class=\"btn btn-sm btn-danger open-add-customer-ruhsat-page\" href=\"{{ route('customers.create',['customer'=>'ruhsat','return_url'=>url()->current(),'type'=>'kurumsal']) }}&unvan=" + $('.search-customer').val() + "\">Yeni Kurumsal Kayıt</a>"
                        $('.modal-footer.cari').html(addCustomerHtml)
                        $('#searchCustomerModal').modal('show')

                        console.log('modal açıldı')
                        $('.filter-results-new .filter-item').on('click', function () {
                            $('input[name=customer_id]').val($(this).data('id'))
                            $('.search-customer').val($(this).data('unvan'));

                            $('#searchCustomerModal').modal('hide')

                            console.log('modal kapandı')
                        })
                    }else{
                        Toast.fire({
                            icon: "error",
                            title: "Eşleşen Kayıt Bulunamadı!"
                        });
                    }
                    $('.customer-search').removeAttr('disabled');
                }
            });
        })


        setInterval(function (){
            $.ajax({
                url: "{{ route('saveToSession') }}",
                type: "post",
                data: $('#mainForm').serialize(),
                success: function (response) {

                },
                error: function(jqXHR, textStatus, errorThrown) {
                    console.log(textStatus, errorThrown);
                }
            });
        },30 * 1000)

        $('.plus_card_price_add').click(function(){
            var stock_id = $('#new_stock_new').val();
            var stock_name = $("#new_stock_new option[value='" + stock_id + "']").text();
            var stock_price = $("#new_stock_new option[value='" + stock_id + "']").data('price');
            var credi = $('#new_credi').val();
            var puan = $('#new_puan').val();
            var puan_branch_id = $('#puan_branches_new').val();
            var puan_branch_name = $("#puan_branches_new option[value='" + puan_branch_id + "']").text();
            $("#new_stock_new option[value='" + stock_id + "']").prop('disabled',true)

            var result = parseInt(stock_price) * parseInt(credi);
            result = result.toLocaleString('tr-TR');
            if(isNaN(result)){
                result = 0;
            }
            var html = '<div class="col-12 mt-2 stock_cont_'+stock_id+'"><div class="row">' +
                '<div class="col-3">' +
                '<label for="stocks_'+stock_id+'">Stok Adı</label><select id="stocks_'+stock_id+'" name="stocks[]" class="form-control" onchange="stock_price_calc_2('+stock_id+')"><option data-price="'+stock_price+'" value="'+stock_id+'">'+stock_name+'</option></select>'+
                '</div>'+
                '<div class="col-2">' +
                '<label for="stock_credi_'+stock_id+'">Kredi</label><input class="form-control" name="stock_price[]" id="stock_credi_'+stock_id+'" value="'+credi+'" type="number" onchange="stock_price_calc_2('+stock_id+')">'+
                '</div>'+
                '<div class="col-2"><label for="new_stock_credi">Puan</label><input class="form-control" name="stock_puan[]" id="stock_puan_'+stock_id+'" value="'+puan+'" type="number"></div>'+
                '<div class="col-3"><label for="stocks_branches'+stock_id+'">Puan Geçerlilik Bayi</label><select id="stocks_branches_'+stock_id+'" name="stocks_branches[]" class="form-control"><option value="'+puan_branch_id+'">'+puan_branch_name+'</option></select></div>'+
                '<div class="col-md-1 d-flex flex-column justify-content-center align-items-start" ><button type="button" data-id="'+stock_id+'" class="btn btn-danger plus_card_stock_delete mt-4"><i class="fa fa-trash"></i></button></div>'+
                '<div class="col-md-1 d-flex flex-column justify-content-center align-items-start">Ödeme <span class="total_price_'+stock_id+'">'+result+'₺</span></div>'
                '</div></div>';

            $('.stock_add_row').append(html);

            $('.plus_card_stock_delete').click(function(){
                var id = $(this).data('id');
                $("#new_stock_new option[value='" + id + "']").prop('disabled',false)
                $('.stock_cont_'+id).remove()
            })
        })

    </script>
@endpush
