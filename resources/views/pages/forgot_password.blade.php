<!DOCTYPE html>
<html lang="{{ config('app.locale') }}">
<head>

    <meta charset="UTF-8">
    <meta name='viewport' content='width=device-width, initial-scale=1.0, user-scalable=0'>
    <meta http-equiv="X-UA-Compatible" content="IE=edge">

    <title>Şifremi Unuttum</title>


    <!-- Favicon -->
    <link rel="icon" href="/assets/logo-icon.png" type="image/x-icon">

    <!-- Main Theme Js -->
    <script src="/assets/js/authentication-main.js"></script>

    <!-- Bootstrap Css -->
    <link id="style" href="/assets/libs/bootstrap/css/bootstrap.min.css" rel="stylesheet" >

    <!-- Style Css -->
    <link href="/assets/css/styles.min.css" rel="stylesheet" >

    <!-- Icons Css -->
    <link href="/assets/css/icons.min.css" rel="stylesheet" >
</head>
<body class="ltr error-page1 bg-danger">

<div class="square-box">
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
    <div></div>
</div>
<div class="page" >
    <div class="page-single">
        <div class="container">
            <div class="row">
                <div class="col-xl-5 col-lg-6 col-md-8 col-sm-8 col-xs-10 card-sigin-main mx-auto my-auto py-4 justify-content-center">
                    <div class="card-sigin">
                        <!-- Demo content-->
                        <div class="main-card-signin d-md-flex">
                            <div class="wd-100p">
                                <div class="d-flex mb-4 justify-content-center"><a href="/"><img src="/assets/umram4.webp" class="sign-favicon ht-80" alt="logo"></a></div>
                                <div class="main-signup-header">
                                    <h2 style="color:#e12222 !important;">Şifre Sıfırla</h2>
                                    @if(isset($_GET['token']) && $_GET['token'] != '')
                                        <h6 class="font-weight-semibold mb-4">Lütfen Yeni Şifrenizi Girin</h6>
                                    @elseif(!empty($_GET['sms_send']) && $_GET['sms_send'] == "success")
                                    <h6 class="font-weight-semibold mb-4">Lütfen Sms Gelen Kodu Girin</h6>
                                    @else
                                        <h6 class="font-weight-semibold mb-4">Lütfen Telefon Adresinizi Girin</h6>
                                    @endif
                                    <form action="{{ route('forgotPasswordPost') }}" method="post">@csrf
                                        @if(isset($_GET['sms_send'] ) && $_GET['sms_send'] == "success")
                                            <div class="form-group">
                                                <label>Şifre Yenileme Kodunuz</label>
                                                <input type="text" class="form-control" placeholder="Şifre Yenileme Kodu" name="sms_code" required>
                                            </div>
                                            <button class="btn btn-dark btn-block">Kodu Doğrula</button>
                                        @elseif(isset($_GET['token']) && $_GET['token'] != '')
                                            <input type="hidden" name="password_reset_token" value="{{ $_GET['token'] }}">
                                            <div class="form-group">
                                                <label>Şifre</label>
                                                <input class="form-control" placeholder="Şifre" name="password" required type="password">
                                            </div>
                                            <div class="form-group">
                                                <label>Şifre Tekrar</label>
                                                <input class="form-control" placeholder="Şifre Tekrar" name="password_again" required type="password">
                                            </div>
                                            <button class="btn btn-dark btn-block">Şifre Sıfırla</button>
                                        @else
                                            <div class="form-group">
                                                <label>Telefon Numaranız</label>
                                                <input class="form-control mask-tel" placeholder="(0XXX) XXX XX XX" name="telephone" value="0" required type="text">
                                            </div>
                                            <button class="btn btn-dark btn-block">Şifre Sıfırlama Bağlantısı Gönder</button>
                                        @endif
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- JQuery min js -->
<script src="/assets/plugins/jquery/jquery.min.js"></script>

<!-- Bootstrap js -->
<script src="/assets/plugins/bootstrap/js/popper.min.js"></script>
<script src="/assets/plugins/bootstrap/js/bootstrap.min.js"></script>

<!-- Moment js -->
<script src="/assets/plugins/moment/moment.js"></script>

<!-- eva-icons js -->
<script src="/assets/js/eva-icons.min.js"></script>

<!-- generate-otp js -->
<script src="/assets/js/generate-otp.js"></script>

<!--Internal  Perfect-scrollbar js -->
<script src="/assets/plugins/perfect-scrollbar/perfect-scrollbar.min.js"></script>

<!-- Theme Color js -->
<script src="/assets/js/themecolor.js"></script>

<!-- custom js -->
<script src="/assets/js/custom.js"></script>

<script src="//cdnjs.cloudflare.com/ajax/libs/toastr.js/latest/toastr.min.js"></script>
<script src="/assets/js/<EMAIL>"></script>
<script src="/assets/js/imask.js"></script>

<script>
    @if(session('success'))
    const Toast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        }
    });
    Toast.fire({
        icon: "success",
        title: "{{ session('success') }}"
    });
    @endif
    @if(session('error'))
    const Toast = Swal.mixin({
        toast: true,
        position: "top-end",
        showConfirmButton: false,
        timer: 3000,
        timerProgressBar: true,
        didOpen: (toast) => {
            toast.onmouseenter = Swal.stopTimer;
            toast.onmouseleave = Swal.resumeTimer;
        }
    });
    Toast.fire({
        icon: "error",
        title: "{{ session('error') }}"
    });
    @endif

    if(document.querySelector('input[name="telephone"]')){
        document.querySelectorAll('input[name="telephone"]').forEach(function (item,index){
            const maskTel = IMask(item, {
                mask: '(0000) 000 0000'
            });
        })
    }
    if(document.querySelector('input[name="sms_code"]')){
        document.querySelectorAll('input[name="sms_code"]').forEach(function (item,index){
            const maskTel = IMask(item, {
                mask: '000000'
            });
        })
    }
    document.querySelector('input[name="telephone"]').addEventListener('keyup',function (){
        let length = this.value.length
        if (length == 0){
            this.value = 0
        }
    })
</script>

</body>
</html>
