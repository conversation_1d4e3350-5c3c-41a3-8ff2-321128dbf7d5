@extends('pages.build')
@section('title','<PERSON><PERSON><PERSON>')
@push('css')
<link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
<link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
<link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
<link href="/assets/css/select2.min.css" rel="stylesheet">
@endpush
@section('content')
<form action="{{ route('branch-categories.store') }}" method="POST">
    @csrf 
    @method('POST')
    <div class="row">
        <div class="col-md-4">
            <div class="accordion" id="accordionPanelsStayOpenExample">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="categoryInfoHeading">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#categoryInfo" aria-expanded="true"
                                aria-controls="categoryInfo">
                            Kategori Bilgileri
                        </button>
                    </h2>
                    <div id="categoryInfo" class="accordion-collapse collapse show" aria-labelledby="categoryInfoHeading">
                        <div class="accordion-body">
                            <div class="row">
                                <div class="col-xl-12">
                                    <label class="form-label">Kategori Adı <span class="text-danger">*</span></label>
                                    <input class="form-control form-control-sm" type="text" name="name" value="" required>
                                </div>
                                <div class="col-12 mt-2">
                                    <label class="form-label">Durum</label>
                                    <select name="status" class="form-control">
                                        <option value="1">Aktif</option>
                                        <option value="0">Pasif</option>
                                    </select>
                                </div>
                                <div class="col-12 mt-2">
                                    <label class="form-label">Sıralama</label>
                                    <input class="form-control form-control-sm" type="number" name="sort" value="" required>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-8">
            <div class="accordion" id="accordionEmployee">
                <div class="accordion-item">
                    <h2 class="accordion-header" id="employeeInfoHeading">
                        <button class="accordion-button" type="button" data-bs-toggle="collapse"
                                data-bs-target="#employeeInfo" aria-expanded="true"
                                aria-controls="employeeInfo">
                            Personel Bilgileri
                        </button>
                    </h2>
                    <div id="employeeInfo" class="accordion-collapse collapse show" aria-labelledby="employeeInfoHeading">
                        <div class="accordion-body">
                            <div class="row">
                                <table id="employeeResponsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                                    <thead>
                                        <tr>
                                            <th style="width:1%">No</th>
                                            <th>Yetki Grubu</th>
                                            <th style="width:5%">Personel Sayısı</th>
                                            <th>Durum</th>
                                            <th>Sıralama</th>
                                            <th>Eklenme Tarihi</th>
                                            <th>Güncellenme Tarihi</th>
                                            <th>İşlemler</th>
                                        </tr>
                                    </thead>
                                    <tbody>
                                    </tbody>
                                    <tfoot id="employeeRow" style="display: none;">
                                        <tr>
                                            <td></td>
                                            <td>
                                                <select disabled name="userLimit[0][group_id]" class="form-control form-control-sm select2">
                                                    @foreach($groups as $group)
                                                        <option value="{{ $group->id }}">{{ $group->name }}</option>
                                                    @endforeach
                                                </select>
                                            </td>
                                            <td>
                                                <input disabled type="text" class="form-control form-control-sm" name="userLimit[0][qty]">
                                            </td>
                                            <td>
                                                <select disabled name="userLimit[0][status]" class="form-control form-control-sm">
                                                    <option value="1">Aktif</option>
                                                    <option value="0">Pasif</option>
                                                </select>
                                            </td>
                                            <td>
                                                <input disabled type="text" class="form-control form-control-sm" name="userLimit[0][sort]">
                                            </td>
                                            <td>
                                                <button type="button" class="btn btn-danger btn-sm remove-user-limit">Sil</button>
                                            </td>
                                            <td></td>
                                            <!-- <th>İşlemler</th> -->
                                        </tr>
                                    </tfoot>
                                </table>
                            </div>
                            <div class="row">
                                <div class="col-12 mt-2">
                                    <button type="button" class="btn btn-sm btn-info add-user-limit">Personel Limiti Ekle</button>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <div class="col-md-12 col-xl-12 col-xs-12 col-sm-12 mt-2">
            <button type="submit" class="btn btn-success mb-3">Kaydet</button>
        </div>
    </div>
</form>
<div class="modal fade" id="userLimitModal" tabindex="-1" aria-labelledby="userLimitModalLabel" aria-hidden="true">
    <div class="modal-dialog modal-dialog-centered">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title" id="userLimitModalLabel">Personel Limiti</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal" aria-label="Close"></button>
            </div>
            <form id="editForm" method="POST">
            @csrf
            <div class="modal-body">
                <div class="row">   
                    <div class="col-12">
                        <div class="form-group">
                            <label>Grup</label>
                            <select name="userLimit[group_id]" class="form-control form-control-sm select2">
                                @foreach($groups as $group)
                                    <option value="{{ $group->id }}">{{ $group->name }}</option>
                                @endforeach
                            </select>
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label>Limit</label>
                            <input type="text" class="form-control form-control-sm" name="userLimit[qty]">
                        </div>
                    </div>
                    <div class="col-12">
                        <div class="form-group">
                            <label>Sıralama</label>
                            <input type="text" class="form-control form-control-sm" name="userLimit[sort]">
                        </div>
                    </div>  
                    <div class="col-12">
                        <div class="form-group">
                            <label>Durum</label>
                            <select name="userLimit[status]" class="form-control form-control-sm">
                                <option value="1">Aktif</option>
                                <option value="0">Pasif</option>
                            </select>
                        </div>
                    </div>
                    <input type="hidden" name="userLimit[id]">
                    <input type="hidden" name="userLimit[url]">
                </div>
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Kapat</button>
                <button type="button" class="btn btn-success save-user-limit">Kaydet</button>
            </div>
            </form>
        </div>
    </div>
</div>
@endsection
<script src="/assets/js/jquery_3.6.1.min.js" ></script>
<script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
<script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
<script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
<script src="/assets/js/select2.min.js"></script>
<script>
    $(document).ready(function () {
        if (typeof Toast === 'undefined') {
            var Toast = Swal.mixin({
                toast: true,
                position: "top-end",
                showConfirmButton: false,
                timer: 3000,
                timerProgressBar: true,
                didOpen: (toast) => {
                    toast.onmouseenter = Swal.stopTimer;
                    toast.onmouseleave = Swal.resumeTimer;
                }
            });
        }

        $('#employeeResponsiveDataTable').DataTable({
            lengthChange: false,
            language: {
                "sEmptyTable": "Tabloda herhangi bir veri mevcut değil",
                "sInfo": "_TOTAL_ kayıttan _START_ - _END_ arası gösteriliyor",
                "sInfoEmpty": "Kayıt yok",
                "sInfoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                "sInfoPostFix": "",
                "sInfoThousands": ".",
                "sLengthMenu": "Sayfada _MENU_ kayıt göster",
                "sLoadingRecords": "Yükleniyor...",
                "sProcessing": "İşleniyor...",
                "sSearch": "Ara:",
                "sZeroRecords": "Eşleşen kayıt bulunamadı",
                "oPaginate": {
                    "sFirst": "İlk",
                    "sLast": "Son",
                    "sNext": "Sonraki",
                    "sPrevious": "Önceki"
                },
                "oAria": {
                    "sSortAscending": ": artan sütun sıralamasını aktifleştirmek için tıklayın",
                    "sSortDescending": ": azalan sütun sıralamasını aktifleştirmek için tıklayın"
                }
            },
        });
        
        var employeeRowIndex = 0;

        $('.add-user-limit').on('click', function () {
            employeeRowIndex++;

            var employeeRow = $('#employeeRow tr').clone().removeAttr('id').show();
            
            employeeRow.find('select, input').each(function() {
                var name = $(this).attr('name');

                if (name) {
                    $(this).prop('disabled', false);

                    name = name.replace('0', employeeRowIndex);
                    $(this).attr('name', name);
                }
            });
            
            $('#employeeResponsiveDataTable tbody').append(employeeRow);
        });
    });
</script>