@extends('pages.build')
@section('title','Araç Vites Türleri')
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
@endpush
@section('excel')
    @if(in_array('*', $userRoles) || in_array('download_excel_car_gear', $userRoles))
        <a href="/excel/vites-turleri" class="btn btn-sm btn-success">Excel Olarak İndir <i class="fa fa-file-excel"></i> </a>
    @endif
    @if(in_array('*', $userRoles) || in_array('upload_excel_car_gear', $userRoles))
        <a href="{{ route('importExcel',['type'=>'car_gear']) }}" class="btn btn-sm btn-danger">Excelden Aktar <i class="fa fa-file-excel"></i> </a>
    @endif
@endsection
@section('content')
    <div class="row">
        <div class="col-xl-12">
            <div class="card custom-card">
                <nav class="nav nav-style-1 nav-pills mb-3" role="tablist">
                    @if(in_array('*', $userRoles) || in_array('add_car_gear', $userRoles))
                        <a href="{{ route('car-gears.create') }}" class="nav-link">Kayıt Ekle</a>
                    @endif
                </nav>
                <div class="card-body">

                    <table id="responsiveDataTable" class="table table-bordered text-nowrap" style="width:100%">
                        <thead>
                        <tr><th style="width:1%" onclick="$('#setColumns').modal('toggle')">Sütunlar</th>
                            <th>Başlık</th>
                            <th>Durum</th>
                            <th>İşlemler</th>
                        </tr>
                        </thead>
                        <tbody>
                        @foreach($items as $key => $item)
                            <tr>
                                <td></td>
                                <td>{{ $item->name }}</td>
                                <td>{{ $item->status == 1 ? 'Aktif' : 'Pasif' }}</td>
                                <td>
                                    @if(in_array('*', $userRoles) || in_array('edit_car_gear', $userRoles))
                                        <a class="btn btn-primary btn-sm" href="{{ route('car-gears.edit',$item) }}">Düzenle</a>
                                    @endif
                                    @if(in_array('*', $userRoles) || in_array('delete_car_gear', $userRoles))
                                        <button class="btn btn-danger btn-sm" type="button" onclick="checkBeforeDelete('deleteForm{{ $key }}')">Sil</button>
                                        <form method="post" action="{{ route('car-gears.destroy',$item) }}" id="deleteForm{{ $key }}">@csrf @method('delete')</form>
                                    @endif
                                </td>
                            </tr>
                        @endforeach
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <!-- Datatables Cdn -->
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>

    <script>
        $(function (e) {
            'use strict';
            var responsiveDatatable = $('#responsiveDataTable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Hiçbir Kayıt Bulunamadı! @if(in_array('*', $userRoles) || in_array('add_car_gear', $userRoles)) <a href='{{ route("car-gears.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Hiçbir Kayıt Bulunamadı! @if(in_array('*', $userRoles) || in_array('add_car_gear', $userRoles)) <a href='{{ route("car-gears.create") }}' class='btn btn-sm btn-danger'>Yeni Kayıt Ekle</a> @endif",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });

            var columns = responsiveDatatable.columns().header().toArray();

            // Her bir sütunun başlığını yazdırma
            let columnsHtml = '<div class="row">'
            columns.forEach(function(column) {
                if (column.cellIndex != 0){
                    columnsHtml += '<div class="col-xl-4 mt-2 form-check-sm">'
                    columnsHtml += '<label class="form-label" for="column'+column.cellIndex+'"><i class="fe fe-corner-down-right"></i>'+$(column).text()+'</label>'
                    columnsHtml += '<input type="checkbox" class="form-check-input column-show-hide" checked id="column'+column.cellIndex+'" value="'+column.cellIndex+'" style="border: 1px solid #0e0e0e">'
                    columnsHtml += "</div>"
                }
            });
            columnsHtml += "</div>"
            $('.datatable-columns').html(columnsHtml)

            $('.column-show-hide').on('click',function (){
                let column = responsiveDatatable.column($(this).val());

                // Toggle the visibility
                column.visible(!column.visible());
            })
        });
    </script>
@endpush
