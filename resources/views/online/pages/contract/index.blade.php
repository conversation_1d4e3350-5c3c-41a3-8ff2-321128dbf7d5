@extends('online.pages.build')
@push('css')
    <link rel="stylesheet" href="/assets/libs/datatable/css/dataTables.bootstrap5.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/responsive.bootstrap.min.css">
    <link rel="stylesheet" href="/assets/libs/datatable/css/buttons.bootstrap5.min.css">
    <link href="/assets/css/select2.min.css" rel="stylesheet">
    <style>
        .select2-container{
            display: block;
            z-index: 99999;
            position: unset;
        }
    </style>
@endpush
@section('content')
    <div class="wrapper">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body">
                            <div class="row">
                                <div class="col-sm-12">
                                    <table id="datatable" class="table table-bordered dt-responsive nowrap dataTable no-footer dtr-inline" style="border-collapse: collapse; border-spacing: 0px; width: 100%;">
                                        <thead>
                                        <tr>
                                            {{--                            <th>Sözleşme No</th>--}}
                                            <th>Sözleşme Türü</th>
                                            {{--                            <th>Başlangıç Tarihi</th>--}}
                                            <th>Sözleşme Süresi</th>
                                            <th>İşlemler</th>
                                            <th>Sözleşme Kodları</th>
                                        </tr>
                                        </thead>
                                        <tbody>
                                        @foreach($items as $key => $item)
                                            <tr>
                                                {{--                                <td>{{ $item->no }}</td>--}}
                                                <td>
                                                    @if($item->type == 'hakedis')
                                                        Hakediş
                                                    @elseif($item->type == 'sabit')
                                                        Sabit
                                                    @elseif($item->type == 'hedefli')
                                                        Hedefli
                                                    @else

                                                    @endif
                                                </td>
                                                {{--                                <td>{{ \Carbon\Carbon::make($item->start_date)->translatedFormat('d F Y H:i') }}</td>--}}
                                                <td>{{ $item->duration_month }} Ay</td>
                                                <td>
                                                    <a class="btn btn-primary btn-sm" href="{{ route('online.contracts.edit',$item) }}">Detaylar</a>
                                                    <a class="btn btn-warning btn-sm" href="{{ route('online.expertises',['type'=>'contract','id'=>$item->no])  }}">Yapılan Ekspertizler</a>
                                                </td>
                                                <td>
                                                    <a class="btn btn-info btn-sm" href="{{ route('online.contractStatus',[$item->id])  }}">Kodlar</a>
                                                    <a class="btn btn-success btn-sm" href="#" onclick="$('#createCodeModal{{ $item->id }}').modal('show')">Kod Üret</a>
                                                    <div class="modal fade" id="createCodeModal{{ $item->id }}" aria-labelledby="notCompleteForm" data-bs-keyboard="false" aria-hidden="true">
                                                        <div class="modal-dialog modal-dialog-centered">
                                                            <div class="modal-content">
                                                                <div class="modal-body">
                                                                    <form id="createContractCodeForm{{ $item->id }}" method="post" action="{{ route('online.createContractCode',$item) }}"> @csrf
                                                                        <!-- <div class="form-group">
                                                                            <label>Sözleşme Tipi</label>
                                                                            <input readonly value="@if($item->type == 'hakedis') Hakediş @elseif($item->type == 'sabit') Sabit @elseif($item->type == 'hedefli') Hedefli @else @endif" form="createContractCodeForm{{ $item->id }}" class="form-control-sm form-control">
                                                                        </div> -->
                                                                        <div class="form-group">
                                                                            <label>Üretilecek Kod Adeti</label>
                                                                            <input form="createContractCodeForm{{ $item->id }}" type="number" name="special_code_count" value="1" min="1" class="form-control-sm form-control">
                                                                        </div>
                                                                        <div class="form-group">
                                                                            <label>Ekspertiz Türü</label>
                                                                            <span class="info-hover ml-2" title="Araca yapılması istenilen ekspertiz türü"><i class="fa fa-info-circle"></i></span>
                                                                            <select class="form-control contract_stock_id form-control-sm" required form="createContractCodeForm{{ $item->id }}" name="contract_stock_id[]">
                                                                                <option value="0">Sözleşme Stokları</option>
                                                                                @foreach(\App\Models\ContractStock::where('contract_id',$item->id)->get()->groupBy('stock_id') as $stockId => $stocks)
                                                                                    <option value="{{ $stocks[0]->stock_id }}"
                                                                                            data-select="@foreach($stocks as $stc)
                                                                        <option value='{{ $stc->type }}'>{{ $stc->type == 'alici' ? 'Alıcı' : ($stc->type == 'satici' ? 'Satıcı' : $item->getCustomer->fullName) }}</option>
                                                                        @endforeach">{{ $stocks[0]->getStock?->ad }}</option>
                                                                                @endforeach
                                                                            </select>
                                                                        </div>
                                                                        <!-- <div class="form-group">
                                                                            <label>Kod Kimin Adına Üretilecek?</label>
                                                                            <select required class="form-control contract_type{{ $item->id }} form-control-sm" form="createContractCodeForm{{ $item->id }}" name="contract_type">
                                                                                <option value="">Seçilmedi</option>
                                                                                @if($item->type == 'sabit' || $item->type == 'hedefli')
                                                                                    <option value="alici">Alıcı</option>
                                                                                @endif
                                                                                <option value="sozlesme_sahibi">Sözleşme Sahibi</option>
                                                                            </select>
                                                                        </div> -->
                                                                        <div class="form-group">
                                                                            <label>Telefon</label> 
                                                                            <input name="telephone" form="createContractCodeForm{{ $item->id }}" readonly disabled class="form-control-sm form-control" value="{{ $item->getCustomer?->phone() }}">
                                                                        </div>
                                                                        <!-- <div class="form-group">
                                                                            <label>Ödeme Alınsın mı?</label>
                                                                            <select required class="form-control contract_must_payment form-control-sm" form="createContractCodeForm{{ $item->id }}" name="contract_must_payment">
                                                                                @if($item->type != 'hedefli')
                                                                                    <option value="1">Evet</option>
                                                                                @endif
                                                                                @if($item->type != 'sabit')
                                                                                    <option value="0">Hayır</option>
                                                                                @endif
                                                                            </select>
                                                                        </div> -->
                                                                        <div class="form-group">
                                                                            <label>Araç Plaka</label> 
                                                                            <span class="info-hover ml-2" title="Ekspertiz yapılacak araç plakası girilmelidir."><i class="fa fa-info-circle"></i></span>
                                                                            <input name="plaka" placeholder="Ekspertiz yapılacak aracın plakasını giriniz" form="createContractCodeForm{{ $item->id }}" class="form-control-sm plaka form-control">
                                                                        </div>
                                                                        <div class="form-group">
                                                                            <label>Geçerli Bayi</label> 
                                                                            <span class="info-hover ml-2" title="Ekspertiz yapılacak bayi seçilmelidir."><i class="fa fa-info-circle"></i></span>
                                                                            <select class="select2" multiple required form="createContractCodeForm{{ $item->id }}" name="contract_branch_id[]">
                                                                                <option value="0">Sözleşme Bayileri</option>
                                                                                @foreach(\App\Models\ContractBranch::where('contract_id',$item->id)->get() as $branch)
                                                                                    <option value="{{ $branch->branch_id }}">{{ $branch->getBranch?->kisa_ad }}</option>
                                                                                @endforeach
                                                                            </select>
                                                                        </div>
                                                                        <button class="btn btn-danger btn-sm">Kod Oluştur</button>
                                                                    </form>
                                                                </div>
                                                            </div>
                                                        </div>
                                                    </div>
                                                </td>
                                            </tr>
                                        @endforeach
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
@endsection
@push('js')
    <script src="/assets/libs/datatable/jquery.dataTables.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.bootstrap5.min.js"></script>
    <script src="/assets/libs/datatable/dataTables.responsive.min.js"></script>
    <script src="/assets/js/select2.min.js"></script>
    <script>
        document.addEventListener("DOMContentLoaded", (event) => {
            $(".select2").select2({
                placeholder: "Bayi adı arayınız..."
            });
        });
        $(function (e) {
            'use strict';
            $('#datatable').DataTable({"dom": '<"pull-left"f><"pull-right"l>rtip',scrollX: true,
                "aaSorting": [],
                "pageLength": 10,
                language: {
                    "emptyTable": "Tabloda herhangi bir veri mevcut değil",
                    "info": "_TOTAL_ kayıttan _START_ - _END_ arasındaki kayıtlar gösteriliyor",
                    "infoEmpty": "Kayıt yok",
                    "infoFiltered": "(_MAX_ kayıt içerisinden bulunan)",
                    "infoThousands": ".",
                    "lengthMenu": "Sayfada _MENU_ kayıt göster",
                    "loadingRecords": "Yükleniyor...",
                    "processing": "İşleniyor...",
                    "search": "Ara:",
                    "zeroRecords": "Eşleşen kayıt bulunamadı",
                    "paginate": {
                        "first": "İlk",
                        "last": "Son",
                        "next": "Sonraki",
                        "previous": "Önceki"
                    },
                    "decimal": ",",
                }
            });
        });

        $('input[name="plaka"]').on('keyup',function (){
            $(this).val($(this).val().replaceAll(' ','').toUpperCase())
        })

        @foreach($items as $item)
        $('.contract_type{{ $item->id }}').on('change', function () {
            let paymentField = $(this).parent().parent().find('.contract_must_payment');
            @if($item->type == 'sabit')
            paymentField.html('<option value="1">Evet</option>');
            @elseif($item->type == 'hakedis')
            if ($(this).val() === 'sozlesme_sahibi') {
                paymentField.html('<option value="0">Hayır</option>');
            } else {
                paymentField.html('<option value="1">Evet</option>');
            }
            @else
            if ($(this).val() === 'sozlesme_sahibi') {
                paymentField.html('<option value="0">Hayır</option>');
            } else {
                paymentField.html('<option value="1">Evet</option>');
            }
            @endif
            if ($(this).val() === 'sozlesme_sahibi'){
                $('input[name="telephone"][form="createContractCodeForm{{ $item->id }}"]').val('{{ $item->getCustomer?->phone() }}').attr('readonly', true)
            }else{
                $('input[name="telephone"][form="createContractCodeForm{{ $item->id }}"]').val('').removeAttr('readonly')
            }
            $('.contract_must_payment').closest('#createContractCodeForm{{ $item->id }}').find('.contract_must_payment').trigger('change');
        });
        $(document).ready(function() {
            // Form gönderme işlemi sırasında required alanları kontrol et
            $('#createContractCodeForm{{ $item->id }}').on('submit', function(event) {
                event.preventDefault();
                let $mustPay = $('select[name="contract_must_payment"]').val();
                if (confirm(`Bu kod için ödeme ${$mustPay == 1 ? 'alınacaktır' : 'alınmayacaktır'}. Onaylıyor musunuz?`)){
                    $('#createContractCodeForm{{ $item->id }}').unbind('submit').submit();
                }
            });
        });
        @endforeach

    </script>
@endpush
