@extends('online.pages.build')
@section('content')
    <div class="wrapper">
        <div class="container-fluid">
            <div class="row BakiyeRow">
                <div class="col-xl-6">
                    <div class="card m-b-20 PlusBakiye">
                        <div class="card-body plus-wrap wrap-tp">
                            <ul class="nav nav-tabs nav-tabs-custom" role="tablist">
                                <li class="nav-item">
                                    <input type="hidden" id="donutokey_0" value="ok">
                                    <a data-key="0" class="tabdonutlink nav-link active" data-toggle="tab" href="#PlusBakiye_0" role="tab">Tümü</a>
                                </li>
                            </ul>
                            <!-- Tab panes -->
                            <div class="tab-content">
                                <div class="tab-pane active p-3" id="PlusBakiye_0" role="tabpanel">
                                    <div class="row text-center m-t-20">
                                        <div class="col-6">
                                            <h5 class="">{{ $plusCardTotalCredit }}</h5>
                                            <p class="text-muted ">Toplam Bakiye</p>
                                        </div>
                                        <div class="col-6">
                                            <h5 class="">{{ $plusCardTotalCredit - $plusCardUsedCredit }}</h5>
                                            <p class="text-muted ">Kalan Bakiye</p>
                                        </div>
                                    </div>

                                    <div id="plusbakiyedonut_0" class="dashboard-charts morris-charts"><svg height="300" version="1.1" width="616" xmlns="http://www.w3.org/2000/svg" xmlns:xlink="http://www.w3.org/1999/xlink" style="overflow: hidden; position: relative; left: -0.796875px; top: -0.59375px;"><desc style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);">Created with Raphaël 2.1.2</desc><defs style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"></defs><path fill="none" stroke="#e74c3c" d="M308.0975,243.33333333333331" stroke-width="2" opacity="1" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 1;"></path><path fill="#e74c3c" stroke="#ffffff" d="M308.0975,246.33333333333331A140,140,0,0,1,308.0975,290Z" stroke-width="3" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"></path><path fill="none" stroke="#2980b9" d="M,0,0" stroke-width="2" opacity="0" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); opacity: 0;"></path><path fill="#2980b9" stroke="#ffffff" d="Z" stroke-width="3" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);"></path><text x="308.0975" y="140" text-anchor="middle" font-family="&quot;Arial&quot;" font-size="15px" stroke="none" fill="#000000" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); text-anchor: middle; font-family: Arial; font-size: 15px; font-weight: 800;" font-weight="800" transform="matrix(1.2772,0,0,1.2772,-85.4007,-41.786)" stroke-width="0.7829706101190476"><tspan style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);" dy="5.25">Kullanılan Bakiye</tspan></text><text x="308.0975" y="160" text-anchor="middle" font-family="&quot;Arial&quot;" font-size="14px" stroke="none" fill="#000000" style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0); text-anchor: middle; font-family: Arial; font-size: 14px;" transform="matrix(1.5954,0,0,1.5954,-183.4541,-89.4651)" stroke-width="0.6267857142857143"><tspan style="-webkit-tap-highlight-color: rgba(0, 0, 0, 0);" dy="4.75">{{ $plusCardUsedCredit }}</tspan></text></svg></div>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-xl-6">
                    <div class="card m-b-20 SonRaporlar" style="height: 510.594px;">

                        <div class="card-body plus-wrap BakiyeDetay Bakiye_0">
                            <div class="table-responsive">
                                <table class="table table-vertical mb-1 ozelTablo">
                                    <thead>
                                    <tr>
                                        <td colspan="5" class="ozelHead"><b>Tümü</b> - Son İşlemler</td>
                                    </tr>
                                    <tr>
                                        <td width="40px">#</td>
                                        <td><b>Şube</b></td>
                                        <td><b>Tarih</b></td>
                                        <td><b>Saat</b></td>
                                        <td width="100px" class="text-center"><b>Detay</b></td>
                                    </tr>
                                    </thead>
                                    <tbody>
                                    @foreach($loads as $index => $load)
                                        <tr>
                                            <td>#{{ $index+1 }}</td>
                                            <td>{{ $load->getPlusCard?->getBranch?->kisa_ad }}</td>
                                            <td>{{ $load->created_at->format('d.m.Y') }}</td>
                                            <td>{{ $load->created_at->format('H:i') }}</td>
                                            <td></td>
                                        </tr>
                                    @endforeach
                                    </tbody>
                                </table>
                            </div>
                        </div>
                    </div>
                </div>



                <div style="display: none; position: absolute; width: 1436.4px;" id="raporDiv" class="col-xl-12 RaporCol">
                    <div class="card m-b-20 RaporOzet" style="height: 510.594px;">
                        <div class="card-body">

                            <div class="row">
                                <div class="col-12">
                                    <div class="invoice-title">
                                        <h4 class="float-right font-16"><strong>Plus Card Ödeme #<em class="siparisNo"></em></strong></h4>
                                        <h3 class="mt-0">
                                            <img src="https://online.umranoto.com.tr/assets/images/logo.png" alt="logo" height="24">
                                        </h3>
                                    </div>
                                    <hr>
                                    <div class="row">
                                        <div class="col-6">
                                            <address>
                                                <strong>Ödeme Yapılan Şube:</strong><br>
                                                <span class="siparisUnvan"></span><br>
                                                <span class="siparisAdres"></span><br>
                                            </address>
                                        </div>
                                        <div class="col-6 text-right">
                                        </div>
                                    </div>
                                    <div class="row">
                                        <div class="col-6 m-t-30">
                                            <address>
                                                <strong>Ödeme Yöntemi:</strong><br>
                                                Plus Card<br>
                                            </address>
                                        </div>
                                        <div class="col-6 m-t-30 text-right">
                                            <address>
                                                <strong>Tarih:</strong><br>
                                                <span class="siparisTarih">00.00.00 00:00</span><br><br>
                                            </address>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="row">
                                <div class="col-12">
                                    <div>
                                        <div class="p-2">
                                            <h3 class="font-16"><strong>Detay</strong></h3>
                                        </div>
                                        <div class="">
                                            <div class="table-responsive">
                                                <table class="table">
                                                    <thead>
                                                    <tr>
                                                        <td><strong>Hizmet</strong></td>
                                                        <td><strong>Araç</strong></td>
                                                        <td><strong>Fiyat</strong></td>
                                                    </tr>
                                                    </thead>
                                                    <tbody>
                                                    <tr>
                                                        <td class="siparisHizmet"></td>
                                                        <td class="siparisArac"></td>
                                                        <td>1 Bakiye</td>
                                                    </tr>
                                                    </tbody>
                                                </table>
                                            </div>

                                            <div class="d-print-none">
                                                <div class="float-right">
                                                    <a href="javascript:printDiv('raporDiv')" id="printBtn" class="btn btn-success waves-effect waves-light hidden-md-down"><i class="fa fa-print"></i> Yazdır</a>
                                                    <a href="javascript:raporKapat()" class="btn btn-danger waves-effect waves-light"><i class="far fa-times-circle"></i> Geri Dön</a>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div> <!-- end row -->

                        </div>
                    </div>
                </div>
                <div class="col-md-12">
                    <div class="card m-b-20">
                        <div class="card-body">

                            <h4 class="mt-0 header-title">Bitmiş Paketlerim</h4>
                            <table id="datatable" class="table table-bordered dt-responsive nowrap" style="border-collapse: collapse; border-spacing: 0; width: 100%;">
                                <thead>
                                <tr>
                                    <th>Paket Adı</th>
                                    <th class="text-right">İşlem Detayları</th>
                                </tr>
                                </thead>
                                <tbody>

                                </tbody>
                            </table>


                        </div>
                    </div>
                </div>

            </div>

        </div>
    </div>
@endsection
