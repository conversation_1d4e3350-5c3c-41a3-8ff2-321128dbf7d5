@extends('online.pages.build')
@section('content')
    <div class="wrapper">
        <div class="container-fluid">
            <div class="row">
                <div class="col-12">
                    <div class="card">
                        <div class="card-body" id="RandevuCard">
                            <h6 class="card-title">Randevu Al</h6>
                            <form action="{{ route('online.bookingStore') }}" method="post">@csrf
                                <div id="Form_Elements">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="PlakaNo"><PERSON>ç <PERSON>lakası</label>
                                                <input type="text" name="plaka" required id="PlakaNo" class="form-control" placeholder="Aracın Plakası" title="Aracın Plakası" />
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="SasiNo"><PERSON>asi Numarası</label>
                                                <input type="text" name="sase_no" required minlength="17" maxlength="17" id="SasiNo" class="form-control" placeholder="Aracın Şasi Numarası" title="Aracın Şasi Numarası" />
                                            </div>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-6">
                                            <div class="form-group">
                                                <label for="Sehir">Şehir</label>
                                                <select name="city_id" required class="form-control" id="Sehir">
                                                    <option value="" selected="">Şehir Seçimi Yapınız</option>
                                                    @foreach($cities as $city)
                                                        <option value="{{ $city->id }}">{{ $city->title }}</option>
                                                    @endforeach
                                                </select>
                                            </div>
                                        </div>
                                        <div class="col-6">
                                            <div class="form-group">
                                                <label for="Sube">Şube</label>
                                                <select class="form-control" required name="branch_id" id="Sube">
                                                    <option value="" selected>Lütfen şehir seçiniz.</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>

                                    <div class="form-group row @if(auth('customer')->check()) d-none @endif">
                                        <label for="Telephone" class="col-md-12 col-form-label">Telefon Numaranız</label>
                                        <div class="col-md-12">
                                            <p class="text-muted m-b-15 step-hide">
                                                Eğer Umran Oto Ekspertiz'den daha önce hizmet aldıysanız, hizmet aldığınız telefon numarasını yazınız.
                                            </p>
                                            <input class="form-control masked-tel" required type="tel" placeholder="0555 444 33 22" id="Telephone" name="telephone" @if(auth('customer')->check()) value="{{ auth('customer')->user()->telefon ?? auth('customer')->user()->cep }}" @endif/>
                                        </div>
                                    </div>

                                    <div class="row">
                                        <div class="col-12 step-hide">
                                            <p>
                                                Randevu saat aralığının başlangıcından itibaren 15 dk öncesinde şubemize gelmeniz gerekmektedir.<br />
                                                Randevu saatinin başlangıcından itibaren en geç 2 saat içinde ekspertiz hizmeti verilecektir.
                                            </p>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="datepicker">Ekspertize Gidilecek Tarih</label>
                                                <div class="input-group">
                                                    <input autocomplete="off" name="date" type="date" min="{{ now()->format('Y-m-d') }}" max="{{ now()->addDays(7)->format('Y-m-d') }}" class="form-control" placeholder="Ay/Gün/Yıl" id="datepicker" />
                                                    <div class="input-group-append">
                                                        <span class="input-group-text"><i class="mdi mdi-calendar"></i></span>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="form-group">
                                                <label for="SaatAraligi">Saat Aralığı</label>
                                                <select class="form-control" required name="hour" id="SaatAraligi">
                                                    <option value="">Lütfen Önce Tarih Seçiniz.</option>
                                                </select>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                                <input type="hidden" name="type" value="normal">
                                <button id="SearchButton" class="btn btn-block btn-primary btn-md"><i class="fa fa-check"></i> Randevu Al</button>
                            </form>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

@endsection
@push('js')
    <script>
        $('select[name="city_id"]').on('change',function (){
            $('select[name="branch_id"]').find('option').remove()
            $('select[name="branch_id"]').append("<option value=\"\">Bayi Seçiniz</option>")
            $('select[name="date"]').find('option').remove()
            $('select[name="date"]').append("<option value=\"\">Tarih Seçiniz</option>")
            $('select[name="hour"]').find('option').remove()
            $('select[name="hour"]').append("<option value=\"\">Saat Seçiniz</option>")
            if ($('select[name="city_id"]').val() > 0){
                $.ajax({
                    url: "{{ route('api.getBranches') }}",
                    type: "post",
                    data: {'_token':'{{ csrf_token() }}','city_id':$('select[name="city_id"]').val()} ,
                    success: function (response) {
                        $('select[name="branch_id"]').css('display','inline-block')

                        $.each(response.items,function (index,item){
                            $('select[name="branch_id"]').append("<option value="+item.id+">"+item.unvan+"</option>")
                        })
                    },
                    error: function(jqXHR, textStatus, errorThrown) {
                        console.log(textStatus, errorThrown);
                    }
                });
            }
        })

        $('select[name="branch_id"]').on('change',function (){
            $('select[name="date"]').find('option').remove().append("<option value=\"0\">Saat Seçiniz</option>")
            $('select[name="hour"]').find('option').remove().append("<option value=\"0\">Saat Seçiniz</option>")
        })

        $('input[name="plaka"]').on('keyup',function (){
            $('input[name="plaka"]').val($('input[name="plaka"]').val().replaceAll(' ','').toUpperCase())
        })
        $('input[name="sase_no"]').on('keyup',function (){
            $('input[name="sase_no"]').val($('input[name="sase_no"]').val().replaceAll(' ','').toUpperCase())
        })

        $('input[name="date"]').on('change',function (){
            let $date = $(this).val()
            let $branch = $('select[name="branch_id"]').val();
            if ($branch == 0){
                const Toast = Swal.mixin({
                    toast: true,
                    position: "top-end",
                    showConfirmButton: false,
                    timer: 3000,
                    timerProgressBar: true,
                    didOpen: (toast) => {
                        toast.onmouseenter = Swal.stopTimer;
                        toast.onmouseleave = Swal.resumeTimer;
                    }
                });
                Toast.fire({
                    icon: "error",
                    title: "Şube Seçilmedi!"
                });
                return false;
            }
            if ($date){
                $('select[name="hour"]').find('option').remove()
                $('select[name="hour"]').append("<option value=\"0\">Saat Seçiniz</option>")
                $.ajax({
                    url: "{{ route('online.branchBookingHours') }}",
                    type: "post",
                    data: {
                        '_token':'{{ csrf_token() }}',
                        'date':$date,
                        'branch_id':$branch
                    } ,
                    success: function (response) {
                        console.log(response.hours)
                        $.each(response.hours,function (index,item){
                            if (item.disabled == 'true')
                                $('select[name="hour"]').append("<option disabled value="+item.id+">"+item.hour+"</option>")
                            else
                                $('select[name="hour"]').append("<option value="+item.id+">"+item.hour+"</option>")
                        })
                    }
                });
            }
        })
    </script>
@endpush
