<div>
    <div class="row">
        <div class="col-12 border-bottom mb-2">
            <div class="row">
                <div class="col-xl-6 col-12 d-flex flex-column justify-content-center align-items-start mt-1">
                    <div class="btn-group" role="group" aria-label="Date Range">
                        <label class="btn btn-outline-primary active-daily-btn daily-btns" for="daily">
                            Günlük
                        </label>
                        <label class="btn btn-outline-primary daily-btns" for="weekly">
                            Haftalık
                        </label>
                        <label class="btn btn-outline-primary daily-btns" for="monthly">
                            Aylık
                        </label>
                        <label class="btn btn-outline-primary daily-btns" for="yearly">
                            Yıllık
                        </label>
                    </div>


                </div>
                <div class="col-xl-6 col-12">
                    <div class="row">
                        <div class="col-lg-5 col-xl-5 col-md-5 col-sm-12 form-group">
                            <label for="">Başlangıç <PERSON></label>
                            <input type="date" id="start_date" value="{{ now()->format('Y-m-d') }}" class="form-control">
                        </div>
                        <div class="col-lg-5 col-xl-5 col-md-5 col-sm-12 form-group">
                            <label for="">Bitiş Tarihi</label>
                            <input type="date" id="finish_date" value="{{ now()->format('Y-m-d') }}" class="form-control">
                        </div>
                        <div
                            class="col-lg-2 col-xl-2 col-md-2 col-sm-12 col-12 d-flex flex-column justify-content-center align-items-start">
                            <span class="span_tool col-12" data-toggle="tooltip" data-placement="top"
                                  title="Arama yapmak için tarih seçin">
                                <button type="button" disabled class="btn btn-sm btn-success mt-3 date-search col-12">
                                    <i class="fa fa-search"></i>
                                </button>
                            </span>

                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="col-12 dashboard-raport-body">
            <div class="row">
                <div class="col-xl-5 col-lg-5 col-md-5 col-sm-12">
                    <div class="row">
                        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
                            <div class="card sales-card relative">
                                <div class="row">
                                    <div class="loader-body cars-loader">
                                        <div class="custom-loader"
                                             style="width: 50px;height: 50px;margin-top: 30%;"></div>
                                    </div>
                                    <div class="col-12">
                                        <div class="ps-4 pt-4 pe-3 pb-4">
                                            <span class="mb-2 fs-12 fw-semibold d-block cars_date_cont"> - <br> Araç Giriş</span>
                                            <div class="pb-0 mt-0">
                                                <div class="d-flex">
                                                    <h4 class="fs-20 fw-semibold mb-2 cars_count_cont">-</h4>
                                                </div>
                                                <p class="mb-0 fs-12 text-muted cars_previous_text">
                                                    <span> - </span>
                                                    <i class="bx bx-caret-up mx-2 text-success"></i>
                                                    <span class="cars_previous_count fw-semibold"> - </span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-xl-6 col-lg-6 col-md-6 col-xs-12">
                            <div class="card sales-card relative">
                                <div class="row">
                                    <div class="loader-body ciro-loader">
                                        <div class="custom-loader"
                                             style="width: 50px;height: 50px;margin-top: 30%;"></div>
                                    </div>
                                    <div class="col-12">
                                        <div class="ps-4 pt-4 pe-3 pb-4">
                                            <span
                                                class="mb-2 fs-12 fw-semibold d-block ciro_date_cont"> - <br> Ciro</span>
                                            <div class="pb-0 mt-0">
                                                <div class="d-flex">
                                                    <h4 class="fs-20 fw-semibold mb-2 ciro_count_cont">-</h4>
                                                </div>
                                                <p class="mb-0 fs-12 text-muted ciro_previous_text">
                                                    <span> - </span>
                                                    <i class="bx bx-caret-up mx-2 text-success"></i>
                                                    <span class="cars_previous_count fw-semibold"> - </span>
                                                </p>
                                            </div>
                                        </div>
                                    </div>

                                </div>
                            </div>
                        </div>
                        <div class="col-xl-12 col-lg-12 col-md-12 col-xs-12">
                            <div class="card" style="height: 450px">
                                <div class="loader-body plus-cards-loader">
                                    <div class="custom-loader" style="width: 50px;height: 50px;margin-top: 30%;"></div>
                                </div>
                                <div class="card-header pb-1"><h3 class="card-title mb-2"><i
                                            class="fe fe-credit-card"></i> Plus Card</h3></div>
                                <div class="card-body p-0">
                                    <div class="browser-stats plus_card_content">
                                        <div class="d-flex align-items-center item  border-bottom my-2">
                                            <div class="d-flex">
                                                <div class="truncate"><h6 class="">Mustafa Gün</h6>
                                                </div>
                                            </div>
                                            <div class="ms-auto my-auto">
                                                <div class="d-flex"><span
                                                        class="me-4 mt-1 fw-semibold fs-16">35,502</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-xl-7 col-lg-7 col-md-7 col-sm-12">
                    <div class="row">
                        <div class="col-sm-12 col-lg-6 col-xl-6">
                            <div class="card overflow-hidden">

                                <div class="loader-body top_branches_loader">
                                    <div class="custom-loader" style="width: 50px;height: 50px;margin-top: 30%;"></div>
                                </div>
                                <div class="card-header pb-1"><h3 class="card-title mb-2">En Yüksek Araç Sayısına Sahip
                                        Bayiler</h3></div>
                                <div class="card-body p-0 customers mt-1" style="min-height: 560px;">
                                    <div class="list-group list-lg-group list-group-flush  top_branches_cont">

                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-sm-12 col-lg-6 col-xl-6">
                            <div class="card overflow-hidden">

                                <div class="loader-body top_branches_ciro_loader">
                                    <div class="custom-loader" style="width: 50px;height: 50px;margin-top: 30%;"></div>
                                </div>
                                <div class="card-header pb-1"><h3 class="card-title mb-2">En Yüksek Ciroya Sahip
                                        Bayiler</h3></div>
                                <div class="card-body p-0 customers mt-1" style="min-height: 560px;">
                                    <div class="list-group list-lg-group list-group-flush  top_branches_ciro_cont">

                                    </div>
                                </div>
                            </div>
                        </div>

                    </div>
                </div>
            </div>
        </div>
        <div class="col-12 ">
            <div class="row">
                {{-- Bölge bazında araç giriş sayıları sadece admin kullanıcılar için gösterilir --}}
                @if(auth()->user()->isAdmin())
                <div class="col-sm-12 col-lg-4 col-xl-4">
                    <div class="card overflow-hidden">
                        <div class="loader-body top_cities_car_loader">
                            <div class="custom-loader" style="width: 50px;height: 50px;margin-top: 30%;"></div>
                        </div>
                        <div class="card-header pb-1"><h3 class="card-title mb-2">Bölge Bazında Araç</h3></div>
                        <div class="card-body p-0 customers mt-1" style="min-height: 560px;">
                            <div class="country-card pt-0  top_cities_car_cont">
                            </div>
                        </div>
                    </div>
                </div>
                <div class="col-sm-12 col-lg-4 col-xl-4">
                    <div class="card overflow-hidden">
                        <div class="loader-body top_cities_ciro_loader">
                            <div class="custom-loader" style="width: 50px;height: 50px;margin-top: 30%;"></div>
                        </div>
                        <div class="card-header pb-1"><h3 class="card-title mb-2">Bölge Bazında Ciro</h3></div>
                        <div class="card-body p-0 customers mt-1" style="min-height: 560px;">
                            <div class="country-card pt-0  top_cities_ciro_cont">
                            </div>
                        </div>
                    </div>
                </div>
                @endif
                {{-- Bayi kullanıcıları için sadece kendi araç giriş sayıları gösterilir --}}
                <div class="@if(auth()->user()->isAdmin()) col-sm-12 col-lg-4 col-xl-4 @else col-sm-12 col-lg-12 col-xl-12 @endif">
                    <div class="card overflow-hidden">
                        <div class="loader-body top_tickets_loader">
                            <div class="custom-loader" style="width: 50px;height: 50px;margin-top: 30%;"></div>
                        </div>
                        <div class="card-header pb-1"><h3 class="card-title mb-2">En Son Açılan Talepler</h3></div>
                        <div class="card-body p-0 customers mt-1" style="min-height: 560px;">
                            <div class="list-group list-lg-group list-group-flush  top_tickets_cont">
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>

</div>

@push('js')
    <script src="{{ asset('assets/js/jquery_3.6.1.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/flatpickr.min.js') }}"></script>
    <script src="{{ asset('assets/libs/flatpickr/l10n/tr.js') }}"></script>
    <script src="{{ asset('assets/js/app.js') }}?v=1.1"></script>
@endpush

@push('css')
    <link rel="stylesheet" href="{{ asset('assets/css/app.css') }}">
@endpush

@push('js')

    <script>
        $(function () {
            $('[data-toggle="tooltip"]').tooltip()
        })
        $(document).ready(function () {

            $('.daily-btns').click(function () {
                $('.daily-btns').removeClass('active-daily-btn')
                $(this).addClass('active-daily-btn')
                $('#start_date').val('')
                $('#finish_date').val('')
                var type = $(this).attr('for');

                getShowLoader()
                getReport(type)
            })
            $('.date-search').click(function () {
                var start_date = $('#start_date').val()
                var finish_date = $('#finish_date').val()
                if ((start_date !== undefined && start_date !== '') || (finish_date !== undefined && finish_date !== '')) {
                    $('.daily-btns').removeClass('active-daily-btn')
                    getShowLoader()
                    getReport(null, start_date, finish_date)
                }
            })
            $('#start_date').change(function () {
                var value = $(this).val()
                if (value !== undefined && value !== '') {
                    searchToolTip(1)
                } else {
                    searchToolTip(2)
                }
            })
            $('#finish_date').change(function () {
                var value = $(this).val()
                if (value !== undefined && value !== '') {
                    searchToolTip(1)
                } else {
                    searchToolTip(2)
                }
            })
            getReport('daily');
        })

        function searchToolTip(type) {
            if (type == 1) {
                //dolu ise
                $('.span_tool').removeAttr('data-toggle')
                $('.span_tool').removeAttr('data-placement')
                $('.span_tool').removeAttr('data-bs-original-title')
                $('[data-toggle="tooltip"]').tooltip()
                $('.date-search').removeAttr('disabled')
            } else {
                $('.span_tool').attr('data-toggle', 'tooltip')
                $('.span_tool').attr('data-placement', 'top')
                $('.span_tool').attr('data-bs-original-title', 'Arama yapmak için tarih seçin')
                $('[data-toggle="tooltip"]').tooltip()
                $('.date-search').attr('disabled', '')
            }
        }

        function getShowLoader() {
            $('.cars-loader').show();
            $('.ciro-loader').show();
            $('.plus-cards-loader').show();
            $('.top_branches_loader').show();
            $('.top_branches_ciro_loader').show();
            // Bölge bazında loader'lar sadece admin kullanıcılar için gösterilir
            @if(auth()->user()->isAdmin())
            $('.top_cities_car_loader').show();
            $('.top_cities_ciro_loader').show();
            @endif
            $('.top_tickets_loader').show();
        }

        function getHideLoader() {
            $('.cars-loader').hide();
            $('.ciro-loader').hide();
            $('.plus-cards-loader').hide();
            $('.top_branches_loader').hide();
            $('.top_branches_ciro_loader').hide();
            // Bölge bazında loader'lar sadece admin kullanıcılar için gizlenir
            @if(auth()->user()->isAdmin())
            $('.top_cities_car_loader').hide();
            $('.top_cities_ciro_loader').hide();
            @endif
            $('.top_tickets_loader').hide();
        }

        function getReport(type = null, start_date = null, finish_date = null) {
            $.ajax({
                url: "{{ route('dashboard_report') }}?{{ isset($_GET['reset']) ? 'reset=reset' : '' }}",
                type: "post",
                data: {
                    '_token': '{{ csrf_token() }}',
                    type: type,
                    start_date: start_date,
                    finish_date: finish_date,
                },
                success: function (response) {
                    if (response.success == "true") {
                        carsCount(response.totalCarCount, response.start_date, response.finish_date, type,response.previous_period_start_date,response.previous_period_finish_date)
                        ciroCount(response.totalCiroCount, response.start_date, response.finish_date, type,response.previous_period_start_date,response.previous_period_finish_date)
                        topBranchCars(response.top_branches, response.start_date, response.finish_date)
                        topBranchCiro(response.top_branches_ciro, response.start_date, response.finish_date)
                        // Bölge bazında veriler sadece admin kullanıcılar için gösterilir
                        @if(auth()->user()->isAdmin())
                        topCitiesCars(response.totalsCitiesCarCount)
                        topCitiesCiro(response.topCitiesCiro)
                        @endif
                        topTickets(response.topTickets)
                        plusCard(response.plus_card_array)
                        getHideLoader()
                    }
                }
            });
        }

        function carsCount(cars, start_date, finish_date, type,previous_start_date,previous_finish_date) {
            var cars_count_date_text = '';
            if (start_date != finish_date) {
                cars_count_date_text = start_date + '-' + finish_date + '<br> Araç Giriş';
            } else {
                cars_count_date_text = start_date + '<br> Araç Giriş'
            }
            $('.cars_date_cont').html(cars_count_date_text)
            $('.cars_count_cont').html(cars.total_car_count)

            var period_difference = cars.total_car_count - cars.previous_period_totalCarCount;

            var period_diffrence_html = '';
            if (type == null) {
                period_diffrence_html += 'Önceki Periyot';
            } else {
                if (type == 'daily') {
                    period_diffrence_html += 'Önceki Gün';
                }
                if (type == 'weekly') {
                    period_diffrence_html += 'Önceki Hafta';
                }
                if (type == 'monthly') {
                    period_diffrence_html += 'Önceki Ay';
                }
                if (type == 'yearly') {
                    period_diffrence_html += 'Önceki Yıl';
                }
            }
            if (previous_finish_date == previous_start_date){
                period_diffrence_html += '('+previous_finish_date+')';
            }else {
                period_diffrence_html += '('+previous_start_date+' - '+previous_finish_date+')';
            }
            if (period_difference < 0) {
                period_diffrence_html += '<i class="bx bx-caret-down mx-2 text-danger"></i> <span class="cars_previous_count fw-semibold text-danger"> ' + period_difference + ' </span>'
            } else {
                period_diffrence_html += '<i class="bx bx-caret-up mx-2 text-success"></i> <span class="cars_previous_count fw-semibold text-success"> ' + period_difference + ' </span>'
            }

            $('.cars_previous_text').html(period_diffrence_html)

        }

        function ciroCount(ciro, start_date, finish_date, type,previous_start_date,previous_finish_date) {
            var ciro_count_date_text = '';
            if (start_date != finish_date) {
                ciro_count_date_text = start_date + '-' + finish_date + '<br> Ciro';
            } else {
                ciro_count_date_text = start_date + '<br> Ciro'
            }
            $('.ciro_date_cont').html(ciro_count_date_text)
            $('.ciro_count_cont').html('₺ ' + ciro.totalCiro)


            var period_difference = ciro.parsedTotalCiro;

            var period_diffrence_html = '';
            if (type == null) {
                period_diffrence_html += 'Önceki Periyot';
            } else {
                if (type == 'daily') {
                    period_diffrence_html += 'Önceki Gün';
                }
                if (type == 'weekly') {
                    period_diffrence_html += 'Önceki Hafta';
                }
                if (type == 'monthly') {
                    period_diffrence_html += 'Önceki Ay';
                }
                if (type == 'yearly') {
                    period_diffrence_html += 'Önceki Yıl';
                }
            }
            if (previous_finish_date == previous_start_date){
                period_diffrence_html += '('+previous_finish_date+')';
            }else {
                period_diffrence_html += '('+previous_start_date+' - '+previous_finish_date+')';
            }
            if (parseFloat(ciro.parsedTotalCiro) < 0) {
                period_diffrence_html += '<i class="bx bx-caret-down mx-2 text-danger"></i> <span class="cars_previous_count fw-semibold text-danger"> ₺ ' + period_difference + ' </span>'
            } else {
                period_diffrence_html += '<i class="bx bx-caret-up mx-2 text-success"></i> <span class="cars_previous_count fw-semibold text-success"> ₺ ' + period_difference + ' </span>'
            }

            $('.ciro_previous_text').html(period_diffrence_html)

        }
        function formatMoney(amount) {
            return amount >= 1000000
                ?  (amount / 1000000).toFixed(2) + 'M '
                : amount >= 1000
                    ?(amount / 1000).toFixed(2) + 'k '
                    : new Intl.NumberFormat('tr-TR', {
                        style: 'decimal',
                    }).format(amount);
        }

        function topBranchCars(data, start_date, end_date) {
            let html = '';
            // Add data[].car_count > 0  cuz there is data but car_count is 0 cuz of left join
            if (data.length > 0 && data[0].car_count > 0) {
                html += `
                    <div class="row px-3 text-center mb-2">
                        <div class="col-3 text-start fw-bold text-muted small"></div>
                        <div class="col-3 fw-bold text-muted small">BİREYSEL</div>
                        <div class="col-3 fw-bold text-muted small">PLUS</div>
                        <div class="col-3 fw-bold text-muted small">TOPLAM</div>
                    </div>`;

                $.each(data, function (index, value) {
                    const bireysel = formatMoney(value.bireysel_count ?? 0) ;

                    const plus = formatMoney( value.plus_card_count ?? 0);

                    const total = formatMoney(value.car_count ?? 0) ;

                    const bireyselClass = value.bireysel_class ?? 'text-dark';
                    const plusClass = value.plus_class ?? 'text-dark';
                    const totalClass = value.total_class ?? 'text-dark';

                    html += `
                        <a href="javascript:void(0);" class="border-0">
                            <div class="list-group-item list-group-item-action p-3 border-0">
                                <div class="row align-items-center text-center">
                                    <div class="col-3 text-start">
                                        <h5 class="mb-1 fs-13 font-weight-sembold text-dark text-break" style="white-space: normal; word-break: break-word;">
                                            <span class="me-1">${index + 1}.</span>${value.kisa_ad}
                                        </h5>
                                    </div>
                                    <div class="col-3 fw-semibold ${bireyselClass}">${bireysel}</div>
                                    <div class="col-3 fw-semibold ${plusClass}">${plus}</div>
                                    <div class="col-3 fw-semibold ${totalClass}">${total}</div>
                                </div>
                            </div>
                        </a>`;

                });

                html += `<a href="{{ route('topBranchCars')}}?start_date=${start_date}&end_date=${end_date}" class="border-0">` +
                    '<div class="list-group-item list-group-item-action p-3 border-0">' +
                    '<div class="media mt-0">' +
                    '<div class="media-body flex-fill">' +
                    '<div class="d-flex align-items-center">' +
                    '<div class="mt-0">' +
                    '<h5 class="mb-1 fs-13 font-weight-sembold text-dark">Tümünü Gör</h5>' +

                    '</div>' +
                    '<span class="ms-auto wd-45p fs-14">' +
                    '<span class="float-end"><i class="fe fe-arrow-right"></i></span>' +
                    '</span>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</a>';
            } else {
                html += '<a href="javascript:void(0);" class="border-0">' +
                    '<div class="list-group-item list-group-item-action p-3 border-0">' +
                    '<div class="media mt-0">' +
                    '<div class="media-body flex-fill">' +
                    '<div class="d-flex align-items-center">' +
                    '<div class="mt-0">' +
                    '<h5 class="mb-1 fs-13 font-weight-sembold text-dark">Herhangi bir veri bulunamadı</h5>' +
                    '</div>' +
                    '<span class="ms-auto wd-45p fs-14">' +
                    '<span class="float-end"></span>' +
                    '</span>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</a>';
            }

            $('.top_branches_cont').html(html);

        }

function topBranchCiro(data, start_date, end_date) {
    let html = '';
    if (data.length > 0) {
        html += `
                    <div class="row px-3 text-center mb-2">
                        <div class="col-3 text-start fw-bold text-muted small"></div>
                        <div class="col-3 fw-bold text-muted small">BİREYSEL</div>
                        <div class="col-3 fw-bold text-muted small">PLUS</div>
                        <div class="col-3 fw-bold text-muted small">TOPLAM</div>
                    </div>`;
        $.each(data, function (index, value) {
            const bireysel = formatMoney(value.bireysel_amount ?? 0);
            const bireyselDiff = value.bireysel_diff ?? 0;
            const bireyselClass = bireyselDiff > 0 ? 'text-success' : (bireyselDiff < 0 ? 'text-danger' : 'text-dark');

            const plus = formatMoney(value.plus_card_amount ?? 0);
            const plusDiff = value.plus_diff ?? 0;
            const plusClass = plusDiff > 0 ? 'text-success' : (plusDiff < 0 ? 'text-danger' : 'text-dark');

            const total = formatMoney(value.total_amount ?? 0);
            const totalDiff = value.total_diff ?? 0;
            const totalClass = totalDiff > 0 ? 'text-success' : (totalDiff < 0 ? 'text-danger' : 'text-dark');

            html += `
                <a href="javascript:void(0);" class="border-0">
                    <div class="list-group-item list-group-item-action p-3 border-0">
                        <div class="row align-items-center text-center">
                            <div class="col-3 text-start">
                                <h5 class="mb-1 fs-13 font-weight-sembold text-dark text-break" style="white-space: normal; word-break: break-word;">
                                    <span class="me-1">${index + 1}.</span>${value.kisa_ad}
                                </h5>
                            </div>
                            <div class="col-3 fw-semibold ${bireyselClass}">${bireysel}</div>
                            <div class="col-3 fw-semibold ${plusClass}">${plus}</div>
                            <div class="col-3 fw-semibold ${totalClass}">${total}</div>
                        </div>
                    </div>
                </a>`;
        });

        html += `
            <a href="{{ route('mostIncomeBranches')}}?start_date=${start_date}&end_date=${end_date}" class="border-0">
                <div class="list-group-item list-group-item-action p-3 border-0">
                    <div class="media mt-0">
                        <div class="media-body flex-fill">
                            <div class="d-flex align-items-center">
                                <div class="mt-0">
                                    <h5 class="mb-1 fs-13 font-weight-sembold text-dark">Tümünü Gör</h5>
                                </div>
                                <span class="ms-auto wd-45p fs-14">
                                    <span class="float-end"><i class="fe fe-arrow-right"></i></span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </a>`;
    } else {
        html += `
            <a href="javascript:void(0);" class="border-0">
                <div class="list-group-item list-group-item-action p-3 border-0">
                    <div class="media mt-0">
                        <div class="media-body flex-fill">
                            <div class="d-flex align-items-center">
                                <div class="mt-0">
                                    <h5 class="mb-1 fs-13 font-weight-sembold text-dark">Herhangi bir veri bulunamadı</h5>
                                </div>
                                <span class="ms-auto wd-45p fs-14">
                                    <span class="float-end"></span>
                                </span>
                            </div>
                        </div>
                    </div>
                </div>
            </a>`;
    }

    $('.top_branches_ciro_cont').html(html);

}

        function topCitiesCars(data) {
            var html = '';
            var total_count_car = 0;
            var hexColors = [
                "#3498db",
                "#2ecc71",
                "#e74c3c",
                "#f39c12",
                "#9b59b6",
                "#1abc9c",
                "#e67e22",
                "#34495e",
                "#27ae60",
                "#d35400"
            ];
            if (data.length > 0) {
                $.each(data, function (index, value) {
                    total_count_car = parseInt(total_count_car) + parseInt(value.car_count)
                })
                $.each(data, function (index, value) {
                    var percentage = parseFloat((value.car_count / total_count_car) * 100).toFixed(2);
                    var randomIndex = Math.floor(Math.random() * hexColors.length);
                    var selectedColor = hexColors[randomIndex];
                    hexColors.splice(randomIndex, 1);


                    html += '<div class="mb-4">' +
                        '<div class="d-flex">' +
                        '<span class="fs-13 fw-semibold">'+value.title+'</span>' +
                        '<div class="ms-auto">' +
                        '<span class="number-font">'+value.car_count+' ('+percentage+'%)</span>' +
                        '</div>' +
                        '</div>' +
                        '<div class="progress ht-7 br-5 mt-2">' +
                        '<div class="progress-bar progress-bar-striped progress-bar-animated" style="width: '+percentage+'%;background-color:'+selectedColor+'"></div>' +
                        '</div>' +
                        '</div>';

                });
            } else {
                html += '<a href="javascript:void(0);" class="border-0">' +
                    '<div class="list-group-item list-group-item-action p-3 border-0">' +
                    '<div class="media mt-0">' +
                    '<div class="media-body flex-fill">' +
                    '<div class="d-flex align-items-center">' +
                    '<div class="mt-0">' +
                    '<h5 class="mb-1 fs-13 font-weight-sembold text-dark">Herhangi bir veri bulunamadı</h5>' +
                    '</div>' +
                    '<span class="ms-auto wd-45p fs-14">' +
                    '<span class="float-end"></span>' +
                    '</span>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</a>';
            }

            $('.top_cities_car_cont').html(html);

        }

        function topCitiesCiro(data) {
            var html = '';
            var total_amount_cities = 0;
            var hexColors = [
                "#3498db",
                "#2ecc71",
                "#e74c3c",
                "#f39c12",
                "#9b59b6",
                "#1abc9c",
                "#e67e22",
                "#34495e",
                "#27ae60",
                "#d35400"
            ];
            if (data.length > 0) {
                $.each(data, function (index, value) {
                    total_amount_cities = parseInt(total_amount_cities) + parseInt(value.total_amount)
                })
                $.each(data, function (index, value) {
                    var total_amount = new Intl.NumberFormat('tr-TR', {
                        style: 'currency',
                        currency: 'TRY'
                    }).format(value.total_amount);
                    var percentage = parseFloat((value.total_amount / total_amount_cities) * 100).toFixed(2);

                    var randomIndex = Math.floor(Math.random() * hexColors.length);
                    var selectedColor = hexColors[randomIndex];
                    hexColors.splice(randomIndex, 1);


                    html += '<div class="mb-4">' +
                        '<div class="d-flex">' +
                        '<span class="fs-13 fw-semibold">'+value.title+'</span>' +
                        '<div class="ms-auto">' +
                        '<span class="number-font">'+total_amount+' ('+percentage+'%)</span>' +
                        '</div>' +
                        '</div>' +
                        '<div class="progress ht-7 br-5 mt-2">' +
                        '<div class="progress-bar progress-bar-striped progress-bar-animated" style="width: '+percentage+'%;background-color:'+selectedColor+'"></div>' +
                        '</div>' +
                        '</div>';

                });
            } else {
                html += '<a href="javascript:void(0);" class="border-0">' +
                    '<div class="list-group-item list-group-item-action p-3 border-0">' +
                    '<div class="media mt-0">' +
                    '<div class="media-body flex-fill">' +
                    '<div class="d-flex align-items-center">' +
                    '<div class="mt-0">' +
                    '<h5 class="mb-1 fs-13 font-weight-sembold text-dark">Herhangi bir veri bulunamadı</h5>' +
                    '</div>' +
                    '<span class="ms-auto wd-45p fs-14">' +
                    '<span class="float-end"></span>' +
                    '</span>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</a>';
            }

            $('.top_cities_ciro_cont').html(html);

        }

        function topTickets(data) {
            html = '';
            if (data.length > 0) {
                $.each(data, function (index, value) {
                    var status_text = ''
                    var status_badge_class = ''
                    if(value.status == 0){
                        status_text = 'Olumsuz Kapandı'
                        status_badge_class ='bg-red'
                    }else if(value.status == 1){
                        status_text = 'Çözümlendi'
                        status_badge_class ='bg-success'
                    }else if(value.status == 2){
                        status_text = 'Bekliyor'
                        status_badge_class ='bg-warning'
                    }else if(value.status == 3){
                        status_text = 'İşlemde'
                        status_badge_class ='bg-warning'
                    }else if(value.status == 4){
                        status_text = 'Mahkemede'
                        status_badge_class ='bg-danger'
                    }else if(value.status == 5){
                        status_text = 'Mahkeme Sonuçlandı'
                        status_badge_class ='bg-danger'
                    }else if(value.status == 6){
                        status_text = 'THHK'
                        status_badge_class ='bg-danger'
                    }

                    html += '<a href="/complaints/'+value.talep_no+'/edit" class="border-0" target="_blank">' +
                        '<div class="list-group-item list-group-item-action p-3 border-0">' +
                        '<div class="media mt-0">' +
                        '<div class="media-body flex-fill">' +
                        '<div class="d-flex align-items-center">' +
                        '<div class="mt-0">' +
                        '<h5 class="mb-1 fs-13 font-weight-sembold text-dark text-ellipsis">' + value.musteri_ifadesi + '</h5>' +

                        '</div>' +
                        '<span class="ms-auto wd-45p fs-14">' +
                        '<span class="float-end badge '+status_badge_class+'">' +
                        status_text +
                        '</span>' +
                        '</span>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '</div>' +
                        '</a>';

                });
            } else {
                html += '<a href="javascript:void(0);" class="border-0">' +
                    '<div class="list-group-item list-group-item-action p-3 border-0">' +
                    '<div class="media mt-0">' +
                    '<div class="media-body flex-fill">' +
                    '<div class="d-flex align-items-center">' +
                    '<div class="mt-0">' +
                    '<h5 class="mb-1 fs-13 font-weight-sembold text-dark">Herhangi bir veri bulunamadı</h5>' +
                    '</div>' +
                    '<span class="ms-auto wd-45p fs-14">' +
                    '<span class="float-end"></span>' +
                    '</span>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</div>' +
                    '</a>';
            }

            $('.top_tickets_cont').html(html);

        }

        function plusCard(data){
            var html1 = '<div class="d-flex align-items-center item  border-bottom my-2"><div class="d-flex"><div class="truncate"><h6 class="">Toplam Plus Card Yüklemesi</h6></div></div><div class="ms-auto my-auto"><div class="d-flex"><span class="me-4 mt-1 fw-semibold fs-16">'+data.total_credi_sales_count+'</span></div></div></div>';
            var html2 = '<div class="d-flex align-items-center item  border-bottom my-2"><div class="d-flex"><div class="truncate"><h6 class="">Toplam Plus Card Cirosu</h6></div></div><div class="ms-auto my-auto"><div class="d-flex"><span class="me-4 mt-1 fw-semibold fs-16">'+data.total_credi_sales_amount.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'})+'</span></div></div></div>';
            var html3 = '<div class="d-flex align-items-center item  border-bottom my-2"><div class="d-flex"><div class="truncate"><h6 class="">Seçilen Tarih Arası Plus Card Yüklemesi</h6></div></div><div class="ms-auto my-auto"><div class="d-flex"><span class="me-4 mt-1 fw-semibold fs-16">'+data.date_total_credi_sales_count+'</span></div></div></div>';
            var html4 = '<div class="d-flex align-items-center item  border-bottom my-2"><div class="d-flex"><div class="truncate"><h6 class="">Seçilen Tarih Arası Plus Card Cirosu</h6></div></div><div class="ms-auto my-auto"><div class="d-flex"><span class="me-4 mt-1 fw-semibold fs-16">'+data.date_total_credi_sales_amount.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'})+'</span></div></div></div>';
            var html5 = '<div class="d-flex align-items-center item  border-bottom my-2"><div class="d-flex"><div class="truncate"><h6 class="">Seçilen Tarih Arası Plus Card Harcaması</h6></div></div><div class="ms-auto my-auto"><div class="d-flex"><span class="me-4 mt-1 fw-semibold fs-16">'+data.used_date_total_credi_count+'</span></div></div></div>';
            var html6 = '<div class="d-flex align-items-center item  border-bottom my-2"><div class="d-flex"><div class="truncate"><h6 class="">Seçilen Tarih Arası Plus Card Harcama Cirosu</h6></div></div><div class="ms-auto my-auto"><div class="d-flex"><span class="me-4 mt-1 fw-semibold fs-16">'+data.used_date_total_credi_amount_count.toLocaleString('tr-TR', {style: 'currency', currency: 'TRY'})+'</span></div></div></div>';
            $('.plus_card_content').html(html1+html2+html3+html4+html5+html6);
        }


    </script>
@endpush
