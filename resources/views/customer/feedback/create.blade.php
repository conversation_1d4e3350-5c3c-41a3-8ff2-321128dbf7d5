@extends('pages.build')
@section('title','<PERSON><PERSON>')
@push('css')
    <link rel="stylesheet" href="/assets/css/dropify.min.css" />
@endpush
@section('content')
    <form method="post" action="{{ route('customer-feedbacks.store') }}" enctype="multipart/form-data">@csrf
        <div class="col-xl-12 mt-3">
            <label class="form-label">Görsel</label><br>
            <span class="text-danger">Geri bildiriminizi görsellerle desteklemek için alttaki kısımdan dosya/kamera izni vererek görsel yükleme yapabilirsiniz.</span>
            <input type="file" data-height="250" name="image" accept="image/*" onclick="return confirmBeforeFileSelect(this);" onchange="handleFileSelect(this)"/>
        </div>
        <div class="col-xl-12">
            <label class="form-label">Mesaj</label>
            <textarea type="text" placeholder="Mesaj" rows="7" class="form-control" name="message"></textarea>
        </div>
        <div class="d-flex align-items-center justify-content-center mt-3" style="flex-direction: column">
            <button class="btn btn-danger">Gönder</button>
        </div>
    </form>
@endsection
@push('js')
    <script src="https://code.jquery.com/jquery-3.7.1.min.js" integrity="sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=" crossorigin="anonymous"></script>
    <script src="/assets/js/dropify.min.js"></script>
    <script>
        $('.dropify').dropify({
            messages: {
                'default': 'Dosya Sürükle veya Tıkla',
                'replace': 'Dosya Sürükle veya Tıkla',
                'remove':  'Kaldır',
                'error':   'Bir Hata Ortaya Çıktı'
            }
        });
        function confirmBeforeFileSelect(input) {
            // Kamera ve dosya erişim izni için onay mesajı
            if (confirm('Umran Oto görsel yükleyebilmeniz için fotoğraflarınıza ve kemaranıza erişim izni istiyor.')) {
                return true; // Onay verildiyse yükleme ekranı açılacak
            } else {
                return false; // Onay verilmezse yükleme ekranı açılmayacak
            }
        }

        function handleFileSelect(input) {
            if (input.files && input.files[0]) {
                const file = input.files[0];

                // Sadece resim dosyalarını kabul et
                if (!file.type.startsWith('image/')) {
                    alert('Lütfen sadece resim dosyası yükleyin.');
                    input.value = ''; // Dosya seçimini temizle
                    return;
                }
            }
        }
    </script>
@endpush
