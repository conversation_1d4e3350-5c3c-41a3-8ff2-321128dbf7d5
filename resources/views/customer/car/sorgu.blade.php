@extends('pages.build')
@section('title','Araç Sorgu')
@push('css')
    <style>
        body {
            font-family: 'Arial', sans-serif;
            margin: 0;
            padding: 0;
            background-color: #fff;
        }

        .search-container {
            position: relative;
            background-color: #fff;
            padding: 20px;
            text-align: center;
        }

        .tabs {
            margin-bottom: 20px;
        }


        input[type="text"] {
            padding: 10px;
            margin-bottom: 20px;
            border: unset;
            border-bottom: 1px solid #000;
            width: 100%;
        }
        #triangle-bottomright {
            position: fixed;
            right: 0;
            bottom: 0;
            width: 0;
            height: 0;
            border-bottom: 300px solid #e30614;
            border-left: 500px solid transparent;
        }
        .paynetj-button{
            display: none;
        }
    </style>
@endpush
@section('content')
    @if(isset($_GET['search']) && $_GET['search'] != '')
        <div class="search-container">
            <div class="card" style="background-color: #22559d9e;">
                <div class="card-body" style="color: #fff">
                    <div class="car-header d-flex justify-content-between">
                        <div class="car-left d-flex">
                            <i style="font-size: 2.5rem" class="ri ri-car-fill"></i>
                            <div class="d-flex" style="flex-direction: column;align-items: baseline;margin-left: 3px">
                                <span>{{ $car_detail['marka'] }}</span>
                                <span>{{ $car_detail['model'] }} - {{ $car_detail['gear'] }}</span>
                            </div>
                        </div>
                        <div class="car-right">
                            <span>Yıl</span>
                            <span>{{ $car_detail['model_yili'] }}</span>
                        </div>
                    </div>
                    <div class="car-footer d-flex justify-content-between">
                        <div class="car-left d-flex">
                            <i style="font-size: 2.5rem" class="fe fe-play"></i>
                            <div class="d-flex" style="flex-direction: column;align-items: baseline;margin-left: 3px">
                                <span>{{ $car_detail['fuel']  }}</span>
                                <span>{{ $car_detail['case_type'] }}</span>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
            <p class="text-center"><b>Ekspertiz Raporları</b></p>
            <p><small>Aracın sistemde bulunan ekspertiz raporları yeniden eskiye doğru sıralanmıştır.</small></p>
            @forelse($expertises as $index => $expertise)
                <div class="card">
                    <div class="card-body">
                        <div class="car-info d-flex justify-content-around">
                            <div class="car-info-left d-flex" style="flex-direction: column">
                                <span style="background-color: #ff1000;color:#fff;padding: 3px 5px;border-radius: 10px">{{ $expertise['plaka'] }}</span>
                                <span>{{ $expertise['sase_no'] }}</span>
                            </div>
                            <div class="car-info-right d-flex" style="flex-direction: column">
                                <span>{{ $expertise['created_at'] }}</span>
                                <span>*** KM</span>
                                <span>{{ $expertise['branch_name'] }}</span>
                            </div>
                        </div>
                    </div>
                    <div class="card-footer text-center">
                        @if($expertise['have_expertise'] != 1)
{{--                            <button type="button" class="btn btn-danger start-pay btn-sm" data-uuid="{{ $expertise['uuid'] }}">Satın Al ({{ $customerBuyExpertisePrice }}₺)</button>--}}
                            <button type="button"
                                    class="btn btn-danger btn-sm paynetButton"
                                    data-amount="{{ $customerBuyExpertisePrice * 100 }}"
                                    data-button_label="Satın Al ({{ $customerBuyExpertisePrice }}₺)"
                                    data-name="Ekspertiz Raporu Satın Al"
                                    data-image="/assets/umram4.webp"
                                    data-agent="1697"
                                    data-index="{{ $index }}"
                                    data-no_instalment="false"
                                    data-tds_required="true"
                                    data-form="#customerBuyExpertisePaynetForm"
                                    data-add_commission_amount="true"
                                    data-reference_no="{{ $expertise['uuid'] }}_{{ auth('customer')->id() }}"
                                    data-description="{{ $expertise['plaka'] }} - {{ $expertise['sase_no'] }} Raporu Satın Alıyorsunuz."
                            >Satın Al ({{ $customerBuyExpertisePrice }}₺)</button>
                        @else
                            <a href = "{{route('customer.ekspertizRaporu',[$expertise['uuid']])}}" class="btn btn-danger btn-sm">Göster</a>
                        @endif
                    </div>
                </div>
            @empty
                <p class="text-danger text-center"><b>Sonuç Bulunamadı!</b></p>
            @endforelse
        </div>
    @else
        <div class="search-container">
            <div class="tabs">
                <a href="{{ route('customer.query',['type'=>'plaka']) }}" class="tab @if(!isset($_GET['type']) || $_GET['type'] == 'plaka') btn-danger @else btn-light @endif btn">Araç Plakası</a>
                <a href="{{ route('customer.query',['type'=>'sase']) }}" class="tab btn @if(isset($_GET['type']) && $_GET['type'] == 'sase') btn-danger @else btn-light @endif">Şase Numarası</a>
            </div>
            <div class="form-container">
                <form action="{{ route('customer.query') }}">
                    <input type="hidden" name="type" @if(!isset($_GET['type']) || $_GET['type'] == 'plaka') value="plaka" @else value="sase" @endif>
                    <input type="text" name="search" placeholder="@if(!isset($_GET['type']) || $_GET['type'] == 'plaka') Araç Plakası @else Şase Numarası @endif Girin">
                    <p class="text-end text-warning">Sorgulamak istediğiniz @if(!isset($_GET['type']) || $_GET['type'] == 'plaka') Araç Plakası @else Şase Numarası @endif giriniz.</p>
                    <button class="btn btn-danger">SORGULA</button>
                </form>

            </div>
        </div>
        <div id="triangle-bottomright"></div>
    @endif

    @if(count($expertises))
        <form id="customerBuyExpertisePaynetForm" action="{{ route('customer.customerBuyExpertisePaynetForm') }}" method="POST">@csrf
            <input type="hidden" name="return_url" value="{{ url()->full() }}">
            <input type="hidden" name="add_comission_amount" value="true">
            <input type="hidden" name="no_instalment" value="false">
            <input type="hidden" name="tds_required" value="true">
            <input type="hidden" name="ratio_code" value="">
            <input type="hidden" name="installments" value="">
            <input type="hidden" name="reference_no" value="">
            <script
                id="paynetScript"
                class="paynet-button"
                type="text/javascript"
                src="https://pj.paynet.com.tr/public/js/paynet.min.js"
                data-key="pbk_rPMxYmDP2b8nhT2aZuhxQqyNZoqg"
                data-amount=""
                data-name="Ekspertiz Raporu Satın Al"
                data-image="/assets/umram4.webp"
                data-agent="1697"
                data-no_instalment="false"
                data-tds_required="true"
                data-form=""
                data-add_commission_amount="true"
                data-reference_no=""
                data-description=""
            >
            </script>
        </form>
    @endif

@endsection
@push('js')
    <script src="/assets/js/jquery_3.6.1.min.js" ></script>
    <script>
        document.querySelectorAll('.paynetButton').forEach(function(button) {
            button.addEventListener('click', function() {
                const $customerBuyExpertisePaynetForm = document.getElementById('customerBuyExpertisePaynetForm');
                $customerBuyExpertisePaynetForm.querySelector('input[name="reference_no"]').value = button.getAttribute('data-reference_no');
                document.querySelectorAll('.paynet-button').forEach(function (script){
                    // Transfer data attributes from the button to the script
                    script.setAttribute('data-amount', button.getAttribute('data-amount'));
                    script.setAttribute('data-button_label', button.getAttribute('data-button_label'));
                    script.setAttribute('data-form', button.getAttribute('data-form'));
                    script.setAttribute('data-reference_no', button.getAttribute('data-reference_no'));
                    script.setAttribute('data-description', button.getAttribute('data-description'));
                })
                // Trigger the click on the paynetj-button element
                document.querySelector('.paynetj-button').click();
            });
        });
    </script>
@endpush
