<?php

namespace App\Exports;

use App\Models\StockType;
use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;
use Maatwebsite\Excel\Concerns\WithStyles;
use PhpOffice\PhpSpreadsheet\Style\Color;
use PhpOffice\PhpSpreadsheet\Style\Fill;
use PhpOffice\PhpSpreadsheet\Worksheet\Worksheet;

class StockTypes extends ExcelHeader<PERSON><PERSON>Adder implements FromCollection , WithHeadings, WithStyles
{
    /**
     * @return \Illuminate\Support\Collection
     */
    public function headings(): array
    {
        return [
            'ID (BOŞ VERİ = YENİ KAYIT)',
            'BAŞLIK',
            'STOKLU SATIŞ(1-EVET 0-HAYIR',
            'DURUM (1-AKTİF 0-PASİF)',
            'EKLENME TARİHİ',
            'DÜZENLEME TARİHİ',
        ];
    }

    public function styles(Worksheet $sheet)
    {
        return [
            1    => [
                'font' => [
                    'bold' => true,
                    'color'=>[
                        'rgb'=>Color::COLOR_WHITE
                    ]
                ],
                'fill' => [
                    'fillType'   => Fill::FILL_SOLID,
                    'startColor' => [
                        'argb' => Color::COLOR_RED
                    ],
                ],
            ],
        ];
    }

    /**
     * @return \Illuminate\Support\Collection
     */
    public function collection()
    {
        $exports = [];
        $items = StockType::all();
        foreach ($items as $item){
            $export = [
                'id' => $item->id,
                'name' => $item->name,
                'stoklu_satis' => $item->stoklu_satis,
                'status' => $item->status,
                'created_at' => $item->created_at,
                'updated_at' => $item->updated_at,
            ];
            array_push($exports,$export);
        }
        return collect($exports);
    }
}
