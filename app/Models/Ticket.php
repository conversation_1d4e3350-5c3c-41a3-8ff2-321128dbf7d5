<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\SoftDeletes;
use <PERSON><PERSON>\Scout\Searchable;

class Ticket extends Model
{
    use HasFactory;
    use Searchable;
    use SoftDeletes;
    public $asYouType = true;

    public function toSearchableArray()
    {
        $searchableArray = [
            'id' => $this->id,
            'uuid' => $this->uuid,
            'subject' => $this->subject
        ];


        return $searchableArray;
    }

    public function getRouteKeyName()
    {
        return 'uuid';
    }

    public function getUser(){
        return $this->hasOne(User::class,'id','user_id');
    }

    public function getRelatedUser(){
        return $this->hasOne(User::class,'id','related_user');
    }

    public function getMessages(){
        return $this->hasMany(TicketMessage::class,'ticket_id','id');
    }

    public function getBranch()
    {
        return $this->hasOne(Branch::class,'id','branch_id');
    }

    public function getImages()
    {
        return $this->hasMany(TicketImage::class,'ticket_id','id');
    }
}
