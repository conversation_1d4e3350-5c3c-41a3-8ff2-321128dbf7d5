<?php

namespace App\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class PlusCardDefinitionStock extends Model
{
    use HasFactory;
    protected $table = 'plus_card_definition_stocks';
    protected $fillable = ['definition_id','stock_id'];
    public function definition()
    {
        return $this->hasOne(PlusCardDefinition::class,'id','definition_id');
    }
    public function stock()
    {
        return $this->hasOne(Stock::class,'id','stock_id');
    }
}
