<?php

namespace App\Http\Controllers;

use App\Models\UserRole;
use App\Models\UserRoleGroup;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Cache;

class UserRoleGroupController extends Controller
{
    public array $arr = [
        'expertise' => [
            'expertise' => 'Ekspertiz',
            'plus_card' => 'Plus Card',
            'contract' => 'Kurumsal Satış',
            'googleseo' => 'Google SEO Buton',
        ],
        'stock' => [
            'stock' => 'Stok',
            'campaign' => 'Kampanya',
            'stock_unit' => 'Stok Birim',
            'stock_group' => 'Stok Grup',
            'stock_type' => 'Stok Tür'
        ],
        'customer' => [
            'customer' => 'Cari', 'customer_type' => 'Cari Tür', 'customer_group' => 'Cari Grup'
        ],
        'branch' => ['branch' => 'Şube', 'zone' => 'Bölge', 'branch_category' => 'Şube Kategori'],
        'car' => [
            'car' => 'Araç',
            'car_group' => 'Araç Grup',
            'car_case_type' => 'Araç Kasa',
            'car_fuel' => 'Araç Yakıt',
            'car_gear' => 'Araç Vites'
        ],
        'user' => ['user' => 'Personel', 'user_group' => 'Personel Grubu'],
        'note' => ['note' => 'Not'],
        'pool_question' => ['pool_question' => 'Anket Soru'],
        'ticket' => ['ticket' => 'Ticket'],
        'complaint' => ['complaint' => 'Talepler', 'customer_feedback' => 'Çek Gönder', 'booking' => 'Randevular'],
        'bulletin' => ['bulletin' => 'Bültenler'],
        'slider' => ['slider' => 'Slider'],
        'definitions' => [
            'definitions' => 'Tanımlar',
            'invoice_definition' => 'Fatura Tanımları',
            'plus_cards_definition' => 'Plus Card Tanımları'
        ],
        'dashboard' => [
            'dashboard' => 'Ana sayfa',
            'page_excel_download' => 'Excel İndirme Sayfası',
        ],
        'report' => ['report' => 'Rapor'],
    ];

    public array $notAdminArr = [
        'expertise' => [
            'expertise' => 'Ekspertiz', 'plus_card' => 'Plus Card'
        ],
        'customer' => [
            'customer' => 'Cari'
        ],
        'car' => ['car' => 'Araç'],
        'user' => ['user' => 'Personel', 'user_group' => 'Personel Grubu'],
        'note' => ['note' => 'Not'],
        'ticket' => ['ticket' => 'Ticket'],
        'complaint' => ['complaint' => 'Şikayetler'],
    ];

    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        if (!auth()->user()->getUserRoleGroup || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key', ['list_user_group'])->first())
            return redirect()->route('index')->with('error', 'Yetkiniz Yok!');

        $items = UserRoleGroup::where('status', '!=', '-1')->get();

        return view('pages.user_role_group.index', ['items' => $items]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        if (!auth()->user()->getUserRoleGroup || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key', ['add_user_group'])->first())
            return redirect()->route('index')->with('error', 'Yetkiniz Yok!');

        return view('pages.user_role_group.create', [
            'arr' => auth()->user()->type == 'admin' || auth()->user()->id == 843 ? $this->arr : $this->notAdminArr
        ]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->getUserRoleGroup
            || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key', ['add_user_group'])->first()
        )
            return redirect()->route('index')->with('error', 'Yetkiniz Yok!');

        $userRoleGroup = new UserRoleGroup();
        $userRoleGroup->name = $request->name;
        $userRoleGroup->status = $request->status;
        $userRoleGroup->save();

        foreach ($request->all() as $key => $value) {
            if ($key != '_token' && $key != 'name' && $key != 'status') {
                $userRole = new UserRole();
                $userRole->group_id = $userRoleGroup->id;
                $userRole->key = $key;
                $userRole->save();
            }
        }

        logRecord(
            "add_user_role_group",
            auth()->user()->name . " adlı kullanıcı " . $userRoleGroup->name . " adlı kullanıcı grubu ekledi.",
            $userRoleGroup->id
        );

        if (Cache::has('user_role_group_create'))
            Cache::forget('user_role_group_create');

        return redirect()->route('user-role-groups.index')->with('success', 'Başarılı');
    }

    /**
     * Display the specified resource.
     */
    public function show(UserRoleGroup $userRoleGroup)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(UserRoleGroup $userRoleGroup)
    {
        if (!auth()->user()->getUserRoleGroup
            || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key', ['edit_user_group'])->first()
        )
            return redirect()->route('index')->with('error', 'Yetkiniz Yok!');

        return view('pages.user_role_group.edit', [
            'userRoleGroup' => $userRoleGroup,
            'arr' => auth()->user()->type == 'admin' || auth()->user()->user_role_group_id == 39 ? $this->arr : $this->notAdminArr
        ]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, UserRoleGroup $userRoleGroup)
    {
        if (!auth()->user()->getUserRoleGroup
            || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key', ['edit_user_group'])->first()
        )
            return redirect()->route('index')->with('error', 'Yetkiniz Yok!');

        if ($request->name != $userRoleGroup->name)
            logRecord(
                "edit_user_role_group",
                auth()->user()->name . " adlı kullanıcı $userRoleGroup->name adlı kullanıcı grubunun adını $request->name olarak güncelledi.",
                $userRoleGroup->id
            );

        $userRoleGroup->name = $request->name;

        if ($request->status != $userRoleGroup->status)
            logRecord(
                "edit_user_role_group",
                auth()->user()->name . " adlı kullanıcı $userRoleGroup->name adlı kullanıcı grubunun durumunu değiştirdi.",
                $userRoleGroup->id
            );

        $userRoleGroup->status = $request->status;
        $userRoleGroup->save();

        if ($userRoleGroup->id == 30) {
            foreach ($request->all() as $key => $value) {
                if ($key != '_token' && $key != 'name' && $key != 'status' && $key != '_method') {
                    $check = UserRole::where(['group_id' => $userRoleGroup->id, 'key' => $key])->first();
                    if (!$check) {
                        $userRole = new UserRole();
                        $userRole->group_id = $userRoleGroup->id;
                        $userRole->key = $key;
                        $userRole->save();
                    }
                }
            }
        } else {
            $userRoles = UserRole::where('group_id', $userRoleGroup->id)->delete();
            foreach ($request->all() as $key => $value) {
                if ($key != '_token' && $key != 'name' && $key != 'status' && $key != '_method') {
                    $userRole = new UserRole();
                    $userRole->group_id = $userRoleGroup->id;
                    $userRole->key = $key;
                    $userRole->save();
                }
            }
        }


        logRecord(
            "edit_user_role_group",
            auth()->user()->name . " adlı kullanıcı $userRoleGroup->name adlı kullanıcının grubunun rollerini değiştirdi.",
            $userRoleGroup->id
        );

        return redirect()->route('user-role-groups.index')->with('success', 'Başarılı');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(UserRoleGroup $userRoleGroup)
    {
        if (!auth()->user()->getUserRoleGroup
            || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key', ['delete_user_group'])->first()
        )
            return redirect()->route('index')->with('error', 'Yetkiniz Yok!');

        if ($userRoleGroup->id != 30) {
            $userRoleGroup->status = -1;
            $userRoleGroup->save();

            logRecord(
                "delete_user_role_group",
                auth()->user()->name . " adlı kullanıcı $userRoleGroup->name adlı kullanıcının grubunu sildi.",
                $userRoleGroup->id
            );
        }

        return redirect()->route('user-role-groups.index')->with('success', 'Başarılı');
    }
}
