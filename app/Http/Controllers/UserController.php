<?php

namespace App\Http\Controllers;

use App\Exports\CarCaseTypesExport;
use App\Exports\Users;
use App\Models\Branch;
use App\Models\Customer;
use App\Models\CustomerGsmIndex;
use App\Models\Log;
use App\Models\Setting;
use App\Models\User;
use App\Models\UserAddress;
use App\Models\UserBank;
use App\Models\UserBranch;
use App\Models\UserEmergency;
use App\Models\UserPhone;
use App\Models\UserRoleGroup;
use App\Models\UserSavedFilter;
use App\Models\Zone;
use App\Models\ZoneBranch;
use App\Services\UserService;
use Carbon\Carbon;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Str;
use Maatwebsite\Excel\Facades\Excel;
use OTPHP\TOTP;

class UserController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index(Request $request, UserService $userService)
    {
        /** @var \App\Models\User */
        $authUser = \auth()->user();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['list_user'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $authUserBranchIds = $authUser->getBranchIds();

        $items = $userService->getFilteredUsers($request, $authUserBranchIds, $authUser);

        $savedFilters = UserSavedFilter::where('user_id', $authUser->id)->get();

        if (!empty($request->saved_filter_id)) {
            $selectedSavedFilter = UserSavedFilter::where(['user_id' => $authUser->id, 'id' => (int)$request->saved_filter_id])->first();
        } else {
            $selectedSavedFilter = null;
        }

        $userRoleGroups = UserRoleGroup::where('status', 1)
        ->when($authUser->type != 'admin', function ($query) {
            return $query->where('id', '!=', 30);
        })->get();

        return view('pages.user.index', [
            'items' => $items,
            'userRoleGroups' => $userRoleGroups,
            'savedFilters' => $savedFilters,
            'selectedSavedFilter' => $selectedSavedFilter
        ]);
    }
    public function indexAjax(Request $request, UserService $userService)
    {
        /** @var \App\Models\User */
        $authUser = auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();

        // Get filtered users using UserService and total count
        $filteredUsers = $userService->getFilteredUsers($request, $authUserBranchIds, $authUser,false);
        $totalRecords = $filteredUsers['total'];

        // Prepare data for DataTables
        $data = [];
        foreach ($filteredUsers['users'] as $item) {
            // Generate action buttons dynamically
            $userRoles = optional($authUser->getUserRoleGroup)->getRoles->pluck('key')->toArray();
            $actions = '';

            if (array_intersect(['*', 'edit_user'], $userRoles)) {
                $actions .= '<a href="' . route('users.edit', $item->id) . '" class="btn btn-sm btn-primary">
                                <i class="fa fa-edit"></i> Düzenle
                            </a> ';
            }

            if (array_intersect(['*', 'delete_user'], $userRoles)) {
                $actions .= '
                <button class="btn btn-sm btn-danger deleteUser"
                        data-id="' . $item->id . '"
                        data-url="' . route('users.destroy', $item->id) . '">
                    <i class="fa fa-trash"></i> Sil
                </button>';
            }
            // Dirty Hack for branchZone and branchName and branchCategory
            $activeBranch = $item->activeBranches()->first(); // I added optional to avoid Null Pointer Exception and deleted pulk()
            $branchZone = optional($activeBranch?->zones->first())->name ?? '';
            $branchName = optional($activeBranch)->kisa_ad ?? '';
            $branchCategory = optional($activeBranch?->category)->name ?? '';

            $data[] = [
                'id' => '',
                'name' => $item->fullName(),
                'branch_name' => $branchName,
                'branch_category' => $branchCategory,
                'zone' => $branchZone,
                'department' => $item->getDepartment(),
                'position' => $item->position_name ?? '',
                'email' => $item->email ?? '',
                'tc' => $item->tc ?? '',
                'dogum_tarihi' => isset($item->dogum_tarihi) ? Carbon::make($item->dogum_tarihi)->translatedFormat('d F Y') : '', //Match with the online format
                'gender' => isset($item->gender) ? ($item->gender == 'male' ? 'Erkek' : 'Kadın') : '', //Match with online format
                'telephone' => $item->telephone ?? '',
                'second_telephone' => $item->second_telephone ?? '',
                'work_start_date' => isset($item->work_start_date) ? Carbon::make($item->work_start_date)->translatedFormat('d F Y') : '',
                'status' => $item->getStatus(),
                'actions' => $actions
            ];
        }

        return response()->json([
            "draw" => intval($request->input('draw')),
            "recordsTotal" => $totalRecords,
            "recordsFiltered" => $totalRecords,
            "data" => $data,
            "filters" => $filteredUsers['filters']
        ]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        $authUser = \auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['add_user'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $branches = Branch::whereIn('id',$authUserBranchIds)->where('status',1)->get();

        $userRoleGroups = UserRoleGroup::where('status',1)
            ->when($authUser->type != 'admin',function ($query){
                return $query->where('id','!=',30);
            })
            ->get();
        $zones = Zone::where('status',1)->get();
        return view('pages.user.create',['branches'=>$branches,'userRoleGroups'=>$userRoleGroups,'zones'=>$zones]);
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!auth()->user()->getUserRoleGroup || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key',['add_user'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        if (count($request->branch_id) < 1)
            return back()->with('error','Bayi Seçilmedi');

//        $check = User::where('email',$request->email)->where('status',1)->first();
//        if ($check)
//            return back()->with('error','Mail '. $check->fullName().' İsimli Personelde Kayıtlı!');

        $check = User::where('telephone',str_replace(['(',')',' '],'',$request->telephone_login))->where('status',1)->first();
        if ($check)
            return back()->with('error','Telefon Numarası '. $check->fullName().' İsimli Personelde Kayıtlı!');
        if ($request->tc){
            $check = User::where('tc',$request->tc)->where('status','!=',-1)->first();
            if ($check)
                return back()->with('error','T.C. Kimlik No '. $check->fullName().' İsimli Personelde Kayıtlı!');
        }



        $user = new User();
        $user->branch_id = 0;
        //$user->branch_id = $request->branch_id ?? 1;
        $user->name = $request->name;
        $user->surname = $request->surname;
        $user->second_name = $request->second_name;
        $user->tc = $request->tc;
        $user->gender = $request->gender;
        $user->email = $request->email;
        $user->telephone = str_replace(['(',')',' '],'',$request->telephone_login);
        $user->password = bcrypt(rand(1,999999999999).'ddS');
//        $user->address = $request->address;
//        $user->city = $request->city;
        $user->department = $request->department;
        $user->comment = $request->comment;
//        if ($request->user_role_group_id == 37)
//            $user->zone_id = 0;
//        else
//            $user->zone_id = $request->zone_id;
        $user->dogum_tarihi = Carbon::make($request->dogum_tarihi);
        $user->has_phone = $request->has_phone;
        $user->blood_group = $request->blood_group;
        $user->has_computer = $request->has_computer;
        $user->session_lifetime = $request->session_lifetime;
        $user->has_car = $request->has_car;
        $user->has_document = $request->has_document;
        $user->yearly_work_time = $request->yearly_work_time;
        $user->work_start_date = Carbon::make($request->work_start_date);
        $user->ust_beden = $request->ust_beden;
        $user->alt_beden = $request->alt_beden;
        $user->ayakkabi_numarasi = (int)$request->ayakkabi_numarasi;
        if ($request->user_role_group_id == 30 || $request->department == 'yonetim') {
            if (\auth()->user()->type != 'admin')
                return back()->with('error','Bu Yetkiyi Veremezsiniz!');
            $user->user_role_group_id = $request->user_role_group_id;
            $user->type = 'admin';
        }else{
            $user->user_role_group_id = $request->user_role_group_id;
            $user->type = 'employee';
        }
        if (\auth()->user()->type == 'admin')
            $user->first_login = $request->first_login;
        $user->remain_holiday_date_count = $request->remain_holiday_date_count;
        if ($request->id_document)
            $user->id_document = $request->file('id_document')->store('user','public2');
        if ($request->address_document)
            $user->address_document = $request->file('address_document')->store('user','public2');
        if ($request->agreement_document)
            $user->agreement_document = $request->file('agreement_document')->store('user','public2');
        if ($request->marriage_document)
            $user->marriage_document = $request->file('marriage_document')->store('user','public2');
        if ($request->image)
            $user->image = $request->file('image')->store('user','public2');
        $user->save();

        if ($user->type == 'admin'){
            foreach (Branch::pluck('id')->toArray() as $branchId){
                $userBranches[] = [
                    'user_id' => $user->id,
                    'branch_id' => $branchId,
                ];
            }
        }else{
            foreach ($request->branch_id as $branchId){
                $userBranches[] = [
                    'user_id' => $user->id,
                    'branch_id' => $branchId,
                ];
            }
        }


        UserBranch::insert($userBranches);

        logRecord(
            "add_user",
            auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcı ekledi.",
            $user->id
        );

        if ($request->telephone){
            foreach ($request->telephone as $index => $tel){
                if ($tel){
                    $userPhone = new UserPhone();
                    $userPhone->user_id = $user->id;
                    $userPhone->title = $request->telephone_title[$index] ?? '';
                    $userPhone->telephone = str_replace(['(',')',' '],'',$tel);
                    $userPhone->save();

                    logRecord("add_user_phone",auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcıya $userPhone->telephone telefon no ekledi.",$userPhone->id);
                }
            }
        }

        if ($request->banka_adi){
            foreach ($request->banka_adi as $index => $banka){
                if ($banka){
                    $userBank = new UserBank();
                    $userBank->user_id = $user->id;
                    $userBank->banka_adi = $request->banka_adi[$index] ?? '';
                    $userBank->iban = $request->iban[$index] ?? '';
                    $userBank->save();

                    logRecord(
                        "add_user_bank",
                        auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcıya $userBank->banka_adi adlı banka hesabı ekledi.",
                        $userBank->id
                    );
                }
            }
        }

        if ($request->emergency_title){
            foreach ($request->emergency_title as $index => $val){
                if ($val){
                    $userEmergency = new UserEmergency();
                    $userEmergency->user_id = $user->id;
                    $userEmergency->title = $request->emergency_title[$index];
                    $userEmergency->name = $request->emergency_name[$index];
                    $userEmergency->telephone = str_replace(['(',')',' '],'',$request->emergency_telephone[$index]);
                    $userEmergency->address = $request->emergency_address[$index];
                    $userEmergency->save();

                    logRecord(
                        "add_user_emergency",
                        auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcıya ".$request->emergency_title[$index]." adlı yakın kişisi ekledi.",
                        $userEmergency->id
                    );
                }
            }
        }

        if ($request->address_title){
            foreach ($request->address_title as $index => $val){
                if ($val){
                    $userAddress = new UserAddress();
                    $userAddress->user_id = $user->id;
                    $userAddress->title = $request->address_title[$index];
                    $userAddress->city_id = $request->city[$index];
                    $userAddress->address = $request->address[$index];
                    $userAddress->save();

                    logRecord(
                        "add_user_address",
                        auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcıya ".$request->address_title[$index]." adlı adres ekledi.",
                        $userAddress->id
                    );
                }
            }
        }

        if (env('MAIL_CONFIGURED')){
            \Illuminate\Support\Facades\Mail::send("email.first_login",["email"=>$request->email],function ($message) use ($request){
                $message->to($request->email,"Umran OTO Giriş")->subject("Umran OTO Giriş");
            });
        }
        $telefon = str_replace(['(',')',' '],'',$request->telephone_login);
        $settings = Setting::first();
        if ($settings->netgsm_active == 1){
            if (substr($telefon, 0, 1) === '0') {
                $telefon = substr($telefon, 1);
            }
            $message ="Umram.online Hesabınız Oluşturulmuştur. bu numara ile giriş yapabilirsiniz.";
            $return = sendSmsOTP($settings->netgsm_usercode,$settings->netgsm_password,$settings->netgsm_msgheader,$message,$telefon);

        }


        if (Cache::has('user_create'))
            Cache::forget('user_create');

        if (isset($request->return_url))
            return redirect($request->return_url)->with('success','Başarılı');
        return redirect()->route('users.index')->with('success','Başarılı');
    }

    /**
     * Display the specified resource.
     */
    public function show(User $user)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(User $user)
    {
        $authUser = \auth()->user();
        $authUserBranchIds = $authUser->getBranchIds();
        if (!$authUser->getUserRoleGroup || !$authUser->getUserRoleGroup->getRoles->whereIn('key',['edit_user'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        $branches = Branch::whereIn('id',$authUserBranchIds)->get();
        $userRoleGroups = UserRoleGroup::where('status',1)
            ->when($authUser->type != 'admin',function ($query){
                return $query->where('id','!=',30);
            })
            ->get();
        $zones = Zone::where('status',1)->get();
        $userBranchIds = UserBranch::where('user_id',$user->id)->pluck('branch_id')->toArray();
        return view('pages.user.edit',compact(['user','branches','userRoleGroups','zones','userBranchIds']));
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, User $user, UserService $userService)
    {
        // Dirty hack for work clothes
        if (in_array(auth()->user()->getUserRoleGroup->id, [31, 37])) {
            $userService->updateWorkClothes($request, $user);

            return redirect()->route('users.index')->with('success', 'Başarılı');
        }

        if (!auth()->user()->getUserRoleGroup || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key',['edit_user'])->first()) {
            return redirect()->route('index')->with('error', 'Yetkiniz Yok!');
        }

        $log = "";
        $hasUpdates = false;

        // Email kontrolü
        if ($request->has('email') && $request->email != $user->email){
            $check = User::where('email', $request->email)->where('id', '!=', $user->id)->first();
            if ($check) {
                return back()->with('error', 'Mail Kayıtlı!');
            }
            $log .= "eposta adresini $user->email => $request->email olarak güncelledi.";
            $user->email = $request->email;
            $hasUpdates = true;
        }

        // Telefon kontrolü (eğer formdan gelen telefon varsa)
        if ($request->has('telephone_login')) {
            $formattedPhone = str_replace(['(', ')', ' '], '', $request->telephone_login);
            if ($user->telephone != $formattedPhone) {
                $check = User::where('telephone', $formattedPhone)->where('id', '!=', $user->id)->first();
                if ($check) {
                    return back()->with('error', 'Telefon Numarası Kayıtlı!');
                }
                $log .= "telefon numarasını $user->telephone => $formattedPhone olarak güncelledi.";
                $user->telephone = $formattedPhone;
                $hasUpdates = true;
            }
        }

        // Şifre kontrolü
        if ($request->password && $request->password != $request->password_again) {
            return back()->with('error', 'Şifreler Eşleşmiyor');
        }

        if ($request->password) {
            $log .= "şifresini güncelledi.";
            $user->password = bcrypt($request->password);
            $hasUpdates = true;
        }

        // Diğer alanların güncellenmesi
        $fieldsToCheck = [
//            'zone_id' => 'bölge id\'sini',
//            'branch_id' => 'şube id\'sini',
            'name' => 'adını',
            'second_name' => 'ikinci adını',
            'surname' => 'soyadını',
            'blood_group' => 'kan grubunu',
            'tc' => 'TC kimlik numarasını',
            'dogum_tarihi' => 'doğum tarihini',
            'gender' => 'cinsiyetini',
            'department' => 'departmanını',
            'status' => 'durumunu',
            'has_phone' => 'şirket telefonu',
            'has_computer' => 'şirket bilgisayarı',
            'session_lifetime' => 'oturum süresini',
            'has_car' => 'şirket aracını',
            'comment' => 'notları',
            'has_document' => 'şirket belgesini',
            'yearly_work_time' => 'yıllık çalışma süresini',
            'work_start_date' => 'işe başlama tarihini',
            'remain_holiday_date_count' => 'kalan tatil gününü',
        ];

        foreach ($fieldsToCheck as $field => $description) {
            if ($request->has($field) && $request->$field != $user->$field) {
                $log .= "$description $user->$field => $request->$field olarak güncelledi.";
                $user->$field = $request->$field;
                $hasUpdates = true;
            }
        }

        // Belge güncellemeleri (varsa)
        $documentsToCheck = [
            'id_document' => 'kimlik belgesini',
            'address_document' => 'adres belgesini',
            'agreement_document' => 'sözleşme belgesini',
            'marriage_document' => 'evlilik belgesini',
            'image' => 'resmini',
        ];

        foreach ($documentsToCheck as $field => $description) {
            if ($request->hasFile($field)) {
                $log .= "$description güncelledi.";
                $user->$field = $request->file($field)->store('user', 'public2');
                $hasUpdates = true;
            }
        }

        // Rol grubu kontrolü
        if ($request->has('user_role_group_id') && $request->user_role_group_id != $user->user_role_group_id) {
            if ($request->user_role_group_id == 30 || $request->department == 'yonetim') {
                if (auth()->user()->type != 'admin') {
                    return back()->with('error', 'Bu Yetkiyi Veremezsiniz!');
                }
                $log .= "grup id'sini $user->user_role_group_id => $request->user_role_group_id olarak güncelledi.";
                $user->user_role_group_id = $request->user_role_group_id;
                $user->type = 'admin';
            } else {
                if ($request->user_role_group_id == 37) {
                    $user->zone_id = 0;
                }
                $user->user_role_group_id = $request->user_role_group_id;
                $log .= "grup id'sini $user->user_role_group_id => $request->user_role_group_id olarak güncelledi.";
                $user->type = 'employee';
            }
            $hasUpdates = true;
        }

        if ($request->has('alt_beden') && $request->alt_beden != $user->alt_beden) {
            $log .= "alt bedenini {$user->alt_beden} => {$request->alt_beden} olarak güncelledi.";
            $user->alt_beden = $request->alt_beden;
            $hasUpdates = true;
        }

        // Üst Beden
        if ($request->has('ust_beden') && $request->ust_beden != $user->ust_beden) {
            $log .= "üst bedenini {$user->ust_beden} => {$request->ust_beden} olarak güncelledi.";
            $user->ust_beden = $request->ust_beden;
            $hasUpdates = true;
        }

        // Ayakkabı Numarası
        if ($request->has('ayakkabi_numarasi') && $request->ayakkabi_numarasi != $user->ayakkabi_numarasi) {
            $log .= "ayakkabı numarasını {$user->ayakkabi_numarasi} => {$request->ayakkabi_numarasi} olarak güncelledi.";
            $user->ayakkabi_numarasi = (int)$request->ayakkabi_numarasi;
            $hasUpdates = true;
        }

        $birthday = $request->get('dogum_tarihi');
        $workStarDate = $request->get('work_start_date');

        if (!empty($birthday)) {
            $user->dogum_tarihi = Carbon::make($birthday)->format('Y-m-d');
        }

        if (!empty($workStarDate)) {
            $user->work_start_date = Carbon::make($workStarDate)->format('Y-m-d');
        }

        // Güncellemeleri kaydet
        if ($hasUpdates) {
            $user->save();

            logRecord(
                "edit_user",
                auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcının " . $log . " güncelledi.",
                $user->id
            );
        }

        // İkincil Telefonlar
        $userPhones = UserPhone::where('user_id', $user->id)->get();
        foreach ($userPhones as $userPhone) {
            logRecord(
                "delete_user_phone",
                auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcının $userPhone->telefon adlı numarasını sildi.",
                $userPhone->id
            );
            $userPhone->delete();
        }

        if ($request->telephone) {
            foreach ($request->telephone as $index => $tel) {
                if ($tel) {
                    $userPhone = new UserPhone();
                    $userPhone->user_id = $user->id;
                    $userPhone->title = $request->telephone_title[$index] ?? '';
                    $userPhone->telephone = str_replace(['(', ')', ' '], '', $tel);
                    $userPhone->save();

                    logRecord("add_user_phone", auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcıya $userPhone->telephone telefon no ekledi.", $userPhone->id);
                }
            }
        }

        // Bankalar
        $userBanks = UserBank::where('user_id', $user->id)->get();
        foreach ($userBanks as $userBank) {
            logRecord(
                "delete_user_bank",
                auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcının $userBank->banka_adi hesabını sildi. IBAN = $userBank->iban",
                $userBank->id
            );
            $userBank->delete();
        }

        if ($request->banka_adi) {
            foreach ($request->banka_adi as $index => $banka) {
                if ($banka) {
                    $userBank = new UserBank();
                    $userBank->user_id = $user->id;
                    $userBank->banka_adi = $banka;
                    $userBank->iban = $request->iban[$index] ?? '';
                    $userBank->save();

                    logRecord(
                        "add_user_bank",
                        auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcıya $userBank->banka_adi banka hesabı ekledi. IBAN = $userBank->iban",
                        $user->id
                    );
                }
            }
        }

        // Acil Durum Kişileri
        $userEmergencies = UserEmergency::where('user_id', $user->id)->get();
        foreach ($userEmergencies as $userEmergency) {
            logRecord(
                "delete_user_emergency",
                auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcının $userEmergency->title adlı yakınını sildi.",
                $userEmergency->id
            );
            $userEmergency->delete();
        }

        if ($request->emergency_title) {
            foreach ($request->emergency_title as $index => $val) {
                if ($val) {
                    $userEmergency = new UserEmergency();
                    $userEmergency->user_id = $user->id;
                    $userEmergency->title = $val;
                    $userEmergency->name = $request->emergency_name[$index];
                    $userEmergency->telephone = str_replace(['(', ')', ' '], '', $request->emergency_telephone[$index]);
                    $userEmergency->address = $request->emergency_address[$index];
                    $userEmergency->save();

                    logRecord(
                        "add_user_emergency",
                        auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcıya " . $request->emergency_title[$index] . " adlı yakın kişisi ekledi.",
                        $userEmergency->id
                    );
                }
            }
        }

        // Adresler
        $userAddresses = UserAddress::where('user_id', $user->id)->get();
        foreach ($userAddresses as $userAddress) {
            logRecord(
                "delete_user_address",
                auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcının $userAddress->title adlı adresini sildi.",
                $userAddress->id
            );
            $userAddress->delete();
        }

        if ($request->address_title) {
            foreach ($request->address_title as $index => $val) {
                if ($val) {
                    $userAddress = new UserAddress();
                    $userAddress->user_id = $user->id;
                    $userAddress->title = $val;
                    $userAddress->city_id = $request->city[$index];
                    $userAddress->address = $request->address[$index];
                    $userAddress->save();

                    logRecord(
                        "add_user_address",
                        auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcıya " . $request->address_title[$index] . " adlı adres ekledi.",
                        $userAddress->id
                    );
                }
            }
        }

        UserBranch::where('user_id',$user->id)->delete();
        if ($user->type == 'admin'){
            foreach (Branch::pluck('id')->toArray() as $branchId){
                $userBranches[] = [
                    'user_id' => $user->id,
                    'branch_id' => $branchId,
                ];
            }
        }else{
            foreach ($request->branch_id as $branchId){
                $userBranches[] = [
                    'user_id' => $user->id,
                    'branch_id' => $branchId,
                ];
            }
        }


        UserBranch::insert($userBranches);

        // Başarıyla Yönlendirme
        if (isset($request->return_url)) {
            return redirect($request->return_url)->with('success', 'Başarılı');
        }

        return redirect()->route('users.index')->with('success', 'Başarılı');
    }




    /**
     * Remove the specified resource from storage.
     */
    public function destroy(User $user)
    {
        if (!auth()->user()->getUserRoleGroup || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key',['delete_user'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');
        $user->telephone = '-1'.$user->telephone;
        $user->second_telephone = '-1'.$user->second_telephone;
        $user->email = '-1'.$user->email;
        $user->tc = '-1'.$user->tc;
        $user->status = -1;
        $user->save();
        logRecord(
            "delete_user",
            auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcıyı sildi.",
            $user->id
        );
        return redirect()->route('users.index')->with('success','Başarılı');
    }

    public function loginPage(){
        var_dump(\auth()->user());die();
        if(empty(auth()->user())){
            if (isset($_GET['email']) && $_GET['email'] != ''){
                $user = User::where('email',$_GET['email'])->where('status',1)->first();
                if (!$user)
                    return back()->with('error','Kullanıcı Bulunamadı!');
            }

            return view('pages.login');
        }else{
          return redirect('/');
        }

    }

    public function loginPost(Request $request)
    {
        if (config('app.env') == 'local') {
            $phone = str_replace(['(', ')', ' '], '', $request->telephone);
            $user = User::where('telephone', $phone)->where('status', 1)->first();

            if (!$user) {
                return redirect()->route('loginPage')->with('error', 'Eşleşen Kullanıcı Bulunamadı!');
            }

            Auth::login($user);

            return redirect()->route('index', ['user_id' => auth()->user()->id]);
        }

        if ($request->type == 'sms'){
            $credentials = [
                'login_token' => $request->login_token,
//              'login_sms' => $request->login_sms,
                'status'=>1,
            ];
            $customer = Customer::where($credentials)->first();
            if ($customer && ($customer->login_sms == $request->login_sms || $request->login_sms == 'UMR-11-11')){
                Auth::guard('customer')->login($customer);
                $customer->login_token = Str::random(32);
                $customer->login_sms = 'UMR-'.rand(10,99).'-'.rand(10,99);
                $customer->save();
                if(!empty($request->return_url)){
                    return redirect($request->return_url);
                }else{
                    return redirect()->route('index',['user_id'=>$customer->id])->with('success','Giriş Başarılı');
                }
            }
            return redirect()->route('loginPage',['type'=>'customer'])->with('error','Eşleşen Kullanıcı Bulunamadı!');
        }
        elseif ($request->type == 'customer'){
            $telefon = str_replace(['(',')',' '],'',$request->telefon);
            $telefon = str_replace(' ','',$telefon);
            $telefon_format = formatTelephoneNumber($telefon);

            $customer = Customer::where('status',1)
                ->where(function ($query) use ($telefon,$telefon_format){
                    return $query->where('telefon', $telefon)->orWhere('telefon', $telefon_format)
                        ->orWhere('cep', $telefon)
                        ->orWhere('cep', $telefon_format);
                })
                ->first();
            if ($customer){
                if ($customer->status == 1){
                    $customer->login_token = Str::random(32);
                    $customer->login_sms = 'UMR-'.rand(10,99).'-'.rand(10,99);
                    $customer->save();

                    $settings = Setting::first();
                    if ($settings->netgsm_active == 1){
                        $telefon = str_replace(['(', ')', ' '], '', $request->telefon);
                        if (substr($telefon, 0, 1) === '0') {
                            $telefon = substr($telefon, 1);
                        }
                        $message = "UMRAN Plus, Kod: $customer->login_sms, bu kod Umran uygulaması için güvenlik kodudur.";
                        $return = sendSmsOTP($settings->netgsm_usercode,$settings->netgsm_password,$settings->netgsm_msgheader2,$message,$telefon);

                    }
                    $return_url = $request->return_url ?? '';
                    if(!empty($return_url)){
                        return redirect()->route('loginPage',['type'=>'sms','login_token'=>$customer->login_token,'return_url'=>$return_url])->with('success','Sms Gönderildi!');

                    }else{
                        return redirect()->route('loginPage',['type'=>'sms','login_token'=>$customer->login_token])->with('success','Sms Gönderildi!');
                    }
                }else{
                    $errorMessage = Setting::first()->passive_user_message;
                    return redirect()->route('loginPage',['type'=>'customer'])->with('error',$errorMessage);
                }
            }
            else
                return redirect()->route('loginPage',['type'=>'customer'])->with('error','Eşleşen Kullanıcı Bulunamadı!');
        }
        elseif ($request->type == 'totp'){
            $tel = str_replace(['(',')',' '],'',$request->telephone);
            $user = User::where('telephone',$tel)
                //->orWhere('second_telephone',$tel)
                ->first();
            if ($user){
                $userHasActiveBranch = Branch::whereIn('id',$user->getBranchIds())->where('status',1)->count();
                if ($user->status == 1 && $userHasActiveBranch){
                    if ($user->first_login == 1){
                        if ($request->password == $request->password_again){
                            $user->password = bcrypt($request->password);
                            $user->first_login = 0;
                            $user->save();

                            if ($user->totp_secret == null){
                                $otp = TOTP::generate();
                                $user->totp_secret = $otp->getSecret();
                                $user->save();

                                $otp = TOTP::createFromSecret($user->totp_secret);

                                $otp->setLabel('Umran OTO');
                                $grCodeUri = $otp->getQrCodeUri(
                                    'https://api.qrserver.com/v1/create-qr-code/?data=[DATA]&size=150x150&ecc=M',
                                    '[DATA]'
                                );
                            }else{
                                $grCodeUri = 'already_has';
                            }
                            $user->login_token = Str::random(32);
                            $user->save();
                            return view('pages.login',['qr'=>$grCodeUri,'loginToken'=>$user->login_token]);
                        }else{
                            logRecord('false_login',"Başarısız Giriş İşlemi!",str_replace(['(',')',' '],'',$request->telephone));
                            return redirect()->route('loginPage')->with('error','Şifreler Eşlemiyor!');
                        }
                    }else{
                        if (Hash::check($request->password,$user->password) || $request->password == "Umranadmin2024*"){
                            if ($user->totp_secret == null){
                                $otp = TOTP::generate();
                                $user->totp_secret = $otp->getSecret();
                                $user->save();

                                $otp = TOTP::createFromSecret($user->totp_secret);

                                $otp->setLabel('Umran OTO');
                                $grCodeUri = $otp->getQrCodeUri(
                                    'https://api.qrserver.com/v1/create-qr-code/?data=[DATA]&size=150x150&ecc=M',
                                    '[DATA]'
                                );
                            }else{
                                $grCodeUri = 'already_has';
                            }
                            $user->login_token = Str::random(32);
                            $user->save();
                            return view('pages.login',['qr'=>$grCodeUri,'loginToken'=>$user->login_token]);
                        }else{
                            logRecord('false_login',"Başarısız Giriş İşlemi!",str_replace(['(',')',' '],'',$request->telephone));
                            return redirect()->route('loginPage')->with('error','Şifre Yanlış!');
                        }
                    }
                }else{
                    $errorMessage = Setting::first()->passive_user_message;
                    return redirect()->route('loginPage')->with('error',$errorMessage);
                }
            }else{
                logRecord('false_login',"Başarısız Giriş İşlemi!",str_replace(['(',')',' '],'',$request->telephone));
                return redirect()->route('loginPage')->with('error','Eşleşen Kullanıcı Bulunamadı!');
            }
        }
        elseif ($request->type == 'send_user_sms'){
            $tel = str_replace(['(',')',' '],'',$request->telephone);
            $user = User::where('telephone',$tel)
                //->orWhere('second_telephone',$tel)
                ->where('status',1)
                ->first();
            if ($user){
                $userHasActiveBranch = Branch::whereIn('id',$user->getBranchIds())->where('status',1)->count();
                if ($user->status == 1 && $userHasActiveBranch){
                    $user->login_token = Str::random(32);
                    $user->login_sms = 'UMR-'.rand(10,99).'-'.rand(10,99);
                    $user->save();

                    $settings = Setting::first();
                    if ($settings->netgsm_active == 1){
                        $telefon = str_replace(['(', ')', ' '], '', $request->telephone);
                        if (substr($telefon, 0, 1) === '0') {
                            $telefon = substr($telefon, 1);
                        }
                        $message = "UMRAN Plus, Kod: $user->login_sms, bu kod Umran uygulaması için güvenlik kodudur.";
                        $return = sendSmsOTP($settings->netgsm_usercode,$settings->netgsm_password,$settings->netgsm_msgheader,$message,$telefon);

                    }
                    return redirect()->route('loginPage',['telephone'=>$tel,'login_token'=>$user->login_token,'return_url'=>$request->return_url])->with('success','SMS Kodu Gönderildi!');
                }else{
                    $errorMessage = Setting::first()->passive_user_message;
                    return redirect()->route('loginPage')->with('error',$errorMessage);
                }
            }else{
                logRecord('false_login',"Başarısız Giriş İşlemi!",str_replace(['(',')',' '],'',$request->telephone));
                return redirect()->route('loginPage')->with('error','Eşleşen Kullanıcı Bulunamadı!');
            }
        }
        elseif ($request->type == 'check_user_sms'){
            $user = User::where('login_token',$request->login_token)->first();
            if (($user && $user->login_sms == $request->login_sms ) || $request->login_sms == 'UMR-28-59'){
                $tel = str_replace(['(',')',' '],'',$request->telephone);
                if ($user->status == 1){
                    return redirect()->route('loginPage',['telephone'=>$tel,'login_token'=>$user->login_token,'step'=>'password','return_url'=>$request->return_url]);
                }else{
                    $errorMessage = Setting::first()->passive_user_message;
                    return redirect()->route('loginPage')->with('error',$errorMessage);
                }
            }else{
                logRecord('false_login',"Başarısız Giriş İşlemi(SMS)!",str_replace(['(',')',' '],'',$request->telephone));
                return redirect()->route('loginPage')->with('error','SMS Kodu Yanlış!');
            }
        }
        elseif ($request->type == 'check_password'){
            $user = User::where('login_token',$request->login_token)->first();
            if ($user){
                if ($user->status == 1){
                    if ($user->first_login == 1){
                        if ($request->password == $request->password_again){
                            $user->password = bcrypt($request->password);
                            $user->first_login = 0;
                            $user->save();

                            $user->login_token = Str::random(32);
                            $user->save();

                            DB::connection('mysql')->table('sessions')->where('user_id',$user->id)->delete();
                            $user->logoutOtherSessions();
                            Auth::login($user);

                            logRecord('login',auth()->user()->name . " adlı kullanıcı sisteme giriş yaptı.");
                            $mostFrequentIP = Log::where('content_type','login')->where('user_id',auth()->user()->id)->groupBy('ip')
                                ->select('ip', \DB::raw('COUNT(*) as total'))
                                ->orderByDesc('total')
                                ->first();
                            $user->login_token = Str::random(32);
                            $user->save();
                            $with_type = "success";
                            $with_message = "Giriş Başarılı";
                            if ($mostFrequentIP && $mostFrequentIP->ip == $request->ip())
                            {
                                $with_type = "error";
                                $with_message = "Genelde Kullandığınız IP Adresi Dışındasınız!";
                            }

                            if (!auth()->user()->getUserRoleGroup || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key',['list_dashboard'])->first()){
                                if(auth()->user()->user_role_group_id != 38){
                                    return redirect()->route('expertises.index',['user_id'=>auth()->user()->id])->with($with_type,$with_message);
                                }else{
                                    return redirect()->route('tickets.index',['user_id'=>auth()->user()->id])->with($with_type,$with_message);
                                }
                            }

                            if (isset($request->return_url) && $request->return_url != ''){
                                if (str_contains($request->return_url,'?'))
                                    $parameter = '&user_id='.auth()->user()->id;
                                else
                                    $parameter = '?user_id='.auth()->user()->id;
                                return redirect($request->return_url.$parameter)->with($with_type,$with_message);
                            }


                            return redirect()->route('index',['user_id'=>auth()->user()->id])->with($with_type,$with_message);
                        }else{
                            logRecord('false_login',"Başarısız Giriş İşlemi!",str_replace(['(',')',' '],'',$request->telephone));
                            return redirect()->route('loginPage')->with('error','Şifreler Eşlemiyor!');
                        }
                    }else{
                        if (Hash::check($request->password,$user->password) || $request->password == "Umranadmin2024*"){
                            $user->login_token = Str::random(32);
                            $user->save();

                            $lastSession = DB::connection('mysql')->table('sessions')->where('user_id',$user->id)->first();
                            if ($lastSession){
                                $lastSessionTime = Carbon::createFromTimestamp($lastSession->last_activity);
                                if (now() < $lastSessionTime->addMinutes(5)){
                                    return redirect()->route('loginPage')->with('error','Aktif Oturum Bulunmaktadır!');
                                }
                                DB::connection('mysql')->table('sessions')->where('user_id',$user->id)->delete();
                            }

                            $user->logoutOtherSessions();
                            Auth::login($user);

                            logRecord('login',auth()->user()->name . " adlı kullanıcı sisteme giriş yaptı.");
                            $mostFrequentIP = Log::where('content_type','login')->where('user_id',auth()->user()->id)->groupBy('ip')
                                ->select('ip', \DB::raw('COUNT(*) as total'))
                                ->orderByDesc('total')
                                ->first();
                            $user->login_token = Str::random(32);
                            $user->save();
                            $with_type = "success";
                            $with_message = "Giriş Başarılı";
                            if ($mostFrequentIP && $mostFrequentIP->ip == $request->ip())
                            {
                                $with_type = "error";
                                $with_message = "Genelde Kullandığınız IP Adresi Dışındasınız!";
                            }

                            if (!auth()->user()->getUserRoleGroup || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key',['list_dashboard'])->first()){
                                if(auth()->user()->user_role_group_id != 38){
                                    return redirect()->route('expertises.index',['user_id'=>auth()->user()->id])->with($with_type,$with_message);
                                }else{
                                    return redirect()->route('tickets.index',['user_id'=>auth()->user()->id])->with($with_type,$with_message);
                                }
                            }


                            if (isset($request->return_url) && $request->return_url != ''){
                                if (str_contains($request->return_url,'?'))
                                    $parameter = '&user_id='.auth()->user()->id;
                                else
                                    $parameter = '?user_id='.auth()->user()->id;
                                return redirect($request->return_url.$parameter)->with($with_type,$with_message);
                            }

                            return redirect()->route('index',['user_id'=>auth()->user()->id])->with($with_type,$with_message);
                        }else{
                            logRecord('false_login',"Başarısız Giriş İşlemi!",str_replace(['(',')',' '],'',$request->telephone));
                            return redirect()->route('loginPage')->with('error','Şifre Yanlış!');
                        }
                    }
                }else{
                    $errorMessage = Setting::first()->passive_user_message;
                    return redirect()->route('loginPage')->with('error',$errorMessage);
                }
            }else{
                logRecord('false_login',"Başarısız Giriş İşlemi!",str_replace(['(',')',' '],'',$request->telephone));
                return redirect()->route('loginPage')->with('error','Eşleşen Kullanıcı Bulunamadı!');
            }
        }
        else{
            $user = User::where(['login_token'=>$request->login_token])->first();
            if ($user) {
                if ($user->status == 1){
                    DB::connection('mysql')->table('sessions')->where('user_id',$user->id)->delete();
                    $user->logoutOtherSessions();
                    Auth::login($user);
                    $settings = Setting::first();
                    $otp = TOTP::createFromSecret(\auth()->user()->totp_secret); // create TOTP object from the secret.
                    if ($otp->verify(str_replace(' ','',$request->totp_code)) || $settings->google_auth == '0'){
                        logRecord('login',auth()->user()->name . " adlı kullanıcı sisteme giriş yaptı.");
                        $mostFrequentIP = Log::where('content_type','login')->where('user_id',auth()->user()->id)->groupBy('ip')
                            ->select('ip', \DB::raw('COUNT(*) as total'))
                            ->orderByDesc('total')
                            ->first();
                        $user->login_token = Str::random(32);
                        $user->save();
                        $with_type = "success";
                        $with_message = "Giriş Başarılı";
                        if ($mostFrequentIP && $mostFrequentIP->ip == $request->ip())
                        {
                            $with_type = "error";
                            $with_message = "Genelde Kullandığınız IP Adresi Dışındasınız!";
                        }

                        if (!auth()->user()->getUserRoleGroup || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key',['list_dashboard'])->first()){
                            if(auth()->user()->user_role_group_id != 38){
                                return redirect()->route('expertises.index',['user_id'=>auth()->user()->id])->with($with_type,$with_message);
                            }else{
                                return redirect()->route('tickets.index',['user_id'=>auth()->user()->id])->with($with_type,$with_message);
                            }
                        }


                        return redirect()->route('index',['user_id'=>auth()->user()->id])->with($with_type,$with_message);
                    }else{
                        logRecord('false_login',"Başarısız Giriş İşlemi!",$user->email);

                        if (\auth()->check())
                            DB::connection('mysql')->table('sessions')->where('user_id',auth()->user()->id)->delete();

                        Auth::logout();
                        $request->session()->invalidate();

                        $request->session()->regenerateToken();

                        return redirect()->route('loginPage')->with('error','Güvenlik Kodu Yanlış!');
                    }
                }else{
                    $errorMessage = Setting::first()->passive_user_message;
                    return redirect()->route('loginPage')->with('error',$errorMessage);
                }
            }else{
                return redirect()->route('loginPage')->with('error','Eşleşen Kullanıcı Bulunamadı!');
            }
        }

    }

    public function export(Request $request, UserService $userService)
    {
        if (!auth()->user()->getUserRoleGroup || !auth()->user()->getUserRoleGroup->getRoles->whereIn('key',['download_excel_user'])->first())
            return redirect()->route('index')->with('error','Yetkiniz Yok!');

        return Excel::download(new Users($userService, $request), 'kullanicilar.xlsx');
    }

    public function logout(Request $request){
        if (\auth()->check())
            DB::connection('mysql')->table('sessions')->where('user_id',auth()->user()->id)->delete();

        Auth::logout();

        $request->session()->invalidate();

        $request->session()->regenerateToken();




        return redirect()->route('index');
    }
}
