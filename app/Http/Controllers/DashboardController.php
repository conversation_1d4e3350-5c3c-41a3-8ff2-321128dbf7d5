<?php

namespace App\Http\Controllers;

use App\Models\Branch;
use App\Models\Car;
use App\Models\Complaint;
use App\Models\Expertise;
use App\Models\ExpertisePayment;
use App\Models\PlusCard;
use App\Models\PlusCardCrediAndPuanAdd;
use App\Models\T202_ARACLAR;
use App\Models\T202ARACLAR;
use App\Models\T400SRVBASLIK;
use App\Models\Ticket;
use App\Models\User;
use App\Services\DashboardService;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Cache;
use Debugbar;
use Illuminate\View\View;

class DashboardController extends Controller
{
    public function __invoke(Request $request)
    {
        if(auth('customer')->check()){
            $plusCards = PlusCard::where('customer_id',\auth('customer')->id())->get();
            $credit = 0;
            foreach ($plusCards as $plusCard){
                $balance = $plusCard->getTotalBalance();
                $credit += $balance['credits'];
            }
            return view('pages.index',['credit'=>$credit]);
        }
        $user = auth()->user();

        if ($user->user_role_group_id == 32 || $user->user_role_group_id == 33 || $user->user_role_group_id == 34)
            return redirect()->route('expertises.index');

        if ($user->user_role_group_id == 38)
            return redirect()->route('tickets.index');

        if (!auth('customer')->check() && !$user->getUserRoleGroup || !$user->getUserRoleGroup->getRoles->whereIn('key',['list_dashboard'])->first()) {
            return redirect()->back()->with('error','Yetkiniz Yok !');
        }
        $daily = [Carbon::now()->startOfWeek(), Carbon::now()->endOfWeek()];
        $weekly = [Carbon::now()->startOfMonth(), Carbon::now()->endOfMonth()];
        $monthly = [Carbon::now()->startOfYear(), Carbon::now()->endOfYear()];
        $yearly = [Carbon::now()->startOfDay(), Carbon::now()->endOfDay()];

        $carQuery =  Expertise::query()
            ->where('status', '!=', -1)
            ->where('manuel_save', 1)
            ->whereNull('deleted_at');
        $expertisePaymentsQuery = ExpertisePayment::query();

        $startDate = Carbon::now()->subWeek()->startOfDay();
        $endDate = Carbon::now()->endOfDay();

//        $mostProfitableBranches = DB::table('branches')
//            ->join('expertises', 'branches.id', '=', 'expertises.branch_id')
//            ->join('expertise_payments', 'expertises.id', '=', 'expertise_payments.expertise_id')
//            ->select('branches.id', 'branches.unvan', DB::raw('SUM(expertise_payments.amount) as total_amount'))
//            ->whereBetween('expertise_payments.created_at', [$startDate, $endDate])
//            ->groupBy('branches.id', 'branches.unvan')
//            ->orderByDesc('total_amount')
//            ->take(5)
//            ->get();

        $totalPayments = ExpertisePayment::whereBetween('created_at', [$startDate, $endDate])->sum('amount');

        if ($request->has('time_range')) {
            $timeRange = $request->input('time_range');

            switch ($timeRange) {
                case 'weekly':
                    $carQuery->whereBetween('created_at', [Carbon::now()->subWeek()->startOfWeek(), Carbon::now()->endOfWeek()]);
                    $expertisePaymentsQuery->whereBetween('created_at', [Carbon::now()->subWeek()->startOfWeek(), Carbon::now()->endOfWeek()]);
                    break;
                case 'monthly':
                    $carQuery->whereMonth('created_at', Carbon::now()->month);
                    $expertisePaymentsQuery->whereMonth('created_at', Carbon::now()->month);
                    break;
                case 'yearly':
                    $carQuery->whereYear('created_at', Carbon::now()->year);
                    $expertisePaymentsQuery->whereYear('created_at', Carbon::now()->year);
                    break;
                default:
                    $carQuery->whereDate('created_at', Carbon::today());
                    $expertisePaymentsQuery->whereDate('created_at', Carbon::today());
                    break;
            }
        }

        $carCount = $carQuery->count();
        $expertisePaymentSum = $expertisePaymentsQuery->sum('amount');

        return view('pages.index', compact('carCount', 'expertisePaymentSum'));
    }

    public function dashboard_report(Request $request){
        $type = $request->type;
        $start_date = $request->start_date;
        $finish_date = $request->finish_date;
        $previous_period_start_date = $request->start_date;
        $previous_period_finish_date = $request->finish_date;
        $user = auth()->user();
        $user_id = $user->id;

        if(!empty($type)){
            if($type == "daily"){
                $start_date = Carbon::now();
                $finish_date = Carbon::now();
                $previous_period_start_date = Carbon::now()->subDays(1)->startOfDay();
                $previous_period_finish_date = Carbon::now()->subDays(1)->endOfDay();
            }elseif($type == "weekly"){
                $start_date = Carbon::now()->startOfWeek();
                $finish_date = Carbon::now();
                $previous_period_start_date = Carbon::now()->subWeek()->startOfWeek();
                $previous_period_finish_date = Carbon::now()->subWeek()->endOfWeek();
            }elseif($type == "monthly"){
                $start_date = Carbon::now()->startOfMonth();
                $finish_date = Carbon::now();
                $previous_period_start_date = Carbon::now()->subMonths(1)->startOfMonth();
                $previous_period_finish_date = Carbon::now()->subMonths()->endOfMonth();
            }elseif($type == "yearly"){
                $start_date = Carbon::now()->startOfYear();
                $finish_date = Carbon::now();
                $previous_period_start_date = Carbon::now()->subYear(1)->startOfYear();
                $previous_period_finish_date = Carbon::now()->subYear()->endOfYear();
            }
        }else{
            $start_date = isset($request->start_date) && $request->start_date != '' ? Carbon::make($request->start_date)->startOfDay() : Carbon::now()->startOfDay();
            $finish_date = isset($request->finish_date) && $request->finish_date != '' ? Carbon::make($request->finish_date)->endOfDay() : (isset($request->start_date) && $request->start_date != '' ? Carbon::make($request->start_date)->endOfDay() : Carbon::now()->endOfDay());

            if($start_date->format('Y-m-d') == $finish_date->format('Y-m-d')){
                $previous_period_start_date = isset($request->start_date) && $request->start_date != '' ? Carbon::make($request->start_date)->subDays() : Carbon::now()->subDays();
                $previous_period_finish_date = $previous_period_start_date;
            }else{
                $date_difference = $finish_date->diffInDays($start_date) + 1;
                $previous_period_start_date = $start_date->copy()->subDays($date_difference)->startOfDay();
                $previous_period_finish_date = $finish_date->copy()->subDays($date_difference)->endOfDay();
            }

        }

        /*ilk ve son saniyesini alma*/

        $start_date = isset($start_date) && $start_date != '' ? $start_date->startOfDay():$start_date;

        $finish_date = isset($finish_date) && $finish_date != '' ? $finish_date->endOfDay():$finish_date;
        $previous_period_start_date = isset($previous_period_start_date) && $previous_period_start_date != '' ? $previous_period_start_date->startOfDay():$previous_period_start_date;
        $previous_period_finish_date = isset($previous_period_finish_date) && $previous_period_finish_date != '' ? $previous_period_finish_date->endOfDay():$previous_period_finish_date;

        $previous_period_start_date = date('Y-m-d H:i:s',strtotime($previous_period_start_date->startOfDay()));
        $previous_period_finish_date = date('Y-m-d H:i:s',strtotime($previous_period_finish_date->endOfDay()));
        $branchKode = null;
        $branchID = $user->getBranchIds();





        $totalCarCount_old = Cache::remember('totalCarCount_old_'.$start_date->startOfDay()."_".$user_id.(isset($_GET['reset']) ? rand(1,99999) :''), 60*60, function () use ($branchID, $start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date,$branchKode) {
            if (!env('APP_LOCAL')) {
                /*return T202ARACLAR::whereHas('t400srvbaslik', function ($query) use ($start_date, $finish_date,$branchKode) {
                    $query->whereBetween('T400_belgetarihi', [$start_date, $finish_date]);
                    if(!empty($branchKode)){
                        $query->whereIn('T400_subekodu',$branchKode);
                    }
                })->count();*/
                return 0;
            }else{
                return 0;
            }
        });


        $start_date = $start_date->startOfDay();
        $finish_date = $finish_date->endOfDay();
        $previous_period_start_date = Carbon::make($previous_period_start_date)->startOfDay();
        $previous_period_finish_date = Carbon::make($previous_period_finish_date)->endOfDay();


        $totalCarCount = Cache::remember('totalCarCounts_'.$start_date."_".$finish_date."_".$user_id.(isset($_GET['reset']) ? rand(1,99999) :''), 60 * 60, function () use ($branchID, $start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date) {
            dd($query);
            return Expertise::when(!empty($branchID),function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
                ->whereBetween('belge_tarihi',[$start_date,$finish_date])
                ->where('status', '!=', -1)
                ->where('manuel_save', 1)
                ->whereNull('deleted_at')
                ->count();
        });
        dd($totalCarCount); die();
        $previous_period_totalCarCount = Cache::remember('previous_period_totalCarCounts_'.$start_date."_".$finish_date."_".$user_id.(isset($_GET['reset']) ? rand(1,99999) :''), 60 * 60, function () use ($branchID, $start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date) {
            return Expertise::when(!empty($branchID),function ($query) use ($branchID){return $query->whereIn('branch_id',$branchID);})
                ->whereBetween('belge_tarihi',[$previous_period_start_date,$previous_period_finish_date])
                ->where('status', '!=', -1)
                ->where('manuel_save', 1)
                ->whereNull('deleted_at')
                ->count();

        });
        $previous_period_totalCarCount_old = Cache::remember('previous_period_totalCarCount_old_'.$start_date."_".$finish_date."_".$user_id.(isset($_GET['reset']) ? rand(1,99999) :''), 60 * 60, function () use ($branchID, $start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date,$branchKode) {
            if (!env('APP_LOCAL')) {
                /*return T202ARACLAR::whereHas('t400srvbaslik', function ($query) use ($previous_period_start_date, $previous_period_finish_date,$branchKode) {
                    $query->whereBetween('T400_belgetarihi', [$previous_period_start_date, $previous_period_finish_date]);
                    if(!empty($branchKode)){
                        $query->whereIn('T400_subekodu',$branchKode);
                    }
                })->count();*/
                return 0;
            }else{
                return 0;
            }
        });

        $totalsCarCount = array(
            'total_car_count' => $totalCarCount+$totalCarCount_old,
            'previous_period_totalCarCount' => $previous_period_totalCarCount+$previous_period_totalCarCount_old,
        );



        $totalCiro_old = Cache::remember('totalCiro_old_' . $start_date."_".$user_id.(isset($_GET['reset']) ? rand(1,99999) :''), 60 * 60, function () use ($branchID, $start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date, $branchKode) {
            if (!env('APP_LOCAL')) {
                /*return T400SRVBASLIK::when(!empty($branchKode),function ($query) use ($branchKode,$previous_period_start_date,$previous_period_finish_date){return $query->whereIn('T400_SRVBASLIK.T400_subekodu',$branchKode);})
                    ->whereBetween('T400_belgetarihi',[$start_date,$finish_date])->sum('T400_tltahsilattutari');*/
                return 0;
            } else {
                return 0;
            }
        });

        $totalPreviousCiro_old = Cache::remember('totalPreviousCiro_old_' . $start_date."_".$user_id.(isset($_GET['reset']) ? rand(1,99999) :''), 60 * 60, function () use ($branchID, $start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date, $branchKode) {
            if (!env('APP_LOCAL')) {
                /*return T400SRVBASLIK::when(!empty($branchKode),function ($query) use ($branchKode,$previous_period_start_date,$previous_period_finish_date){return $query->whereIn('T400_SRVBASLIK.T400_subekodu',$branchKode);})
                    ->whereBetween('T400_belgetarihi',[$previous_period_start_date,$previous_period_finish_date])->sum('T400_tltahsilattutari');*/
                return 0;
            } else {
                return 0;
            }
        });
        $totalCiro = Cache::remember('totalCiros_' . $start_date."_".$user_id.(isset($_GET['reset']) ? rand(1,99999) :''), 60 * 60, function () use ($branchID, $start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date) {
            return Expertise::when(!empty($branchID), function ($query) use ($branchID) {
                return $query->whereIn('branch_id', $branchID);
            })
                ->join('expertise_payments', 'expertises.id', '=', 'expertise_payments.expertise_id')
                ->whereNull('expertise_payments.deleted_at')
                ->whereBetween('expertises.created_at', [$start_date, $finish_date])
                ->selectRaw('SUM(expertise_payments.amount) as total_amount')
                ->first();
        });
        $totalPreviousCiro = Cache::remember('totalPreviousCiros_' . $start_date."_".$user_id.(isset($_GET['reset']) ? rand(1,99999) :''), 60 * 60, function () use ($branchID, $start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date) {
            return Expertise::when(!empty($branchID), function ($query) use ($branchID) {
                return $query->whereIn('branch_id', $branchID);
            })
                ->join('expertise_payments', 'expertises.id', '=', 'expertise_payments.expertise_id')
                ->whereNull('expertise_payments.deleted_at')
                ->whereBetween('expertises.created_at', [$previous_period_start_date, $previous_period_finish_date])
                ->selectRaw('SUM(expertise_payments.amount) as total_amount')
                ->first();
        });



        $totalCiroData = array(
            'totalCiro' => number_format($totalCiro->total_amount + $totalCiro_old, 2, ',', '.'),
            'totalPreviousCiro' => number_format($totalPreviousCiro->total_amount + $totalPreviousCiro_old, 2, ',', '.'),
            'parsedTotalCiro' => number_format((($totalCiro->total_amount + $totalCiro_old) - ($totalPreviousCiro->total_amount + $totalPreviousCiro_old)), 2, ',', '.')
        );


        $top_branches_ciro = Cache::remember('top_branches_ciro_' . $start_date . "_" . $user_id . (isset($_GET['reset']) ? rand(1, 99999) : ''), 60 * 60, function () use ($branchID, $start_date, $finish_date) {

            $previous_period_start_date = $start_date->copy()->subWeek()->startOfDay();// one week ago of the given start date and end date
            $previous_period_finish_date = $finish_date->copy()->subWeek()->endOfDay();

            // specify the previous date as one week

            $current = app(DashboardService::class)->mostIncomeBranches([
                'branchIds' => $branchID,
                'startDate' => $start_date,
                'endDate' => $finish_date,
            ]);

            $previous = app(DashboardService::class)->mostIncomeBranches([
                'branchIds' => $branchID,
                'startDate' => $previous_period_start_date,
                'endDate' => $previous_period_finish_date,
            ])->keyBy('branch_id');

            $final = $current->map(function ($item) use ($previous) {
                $prev = $previous[$item['branch_id']] ?? null;

                $item['bireysel_diff'] = $item['bireysel_amount'] - ($prev['bireysel_amount'] ?? 0);
                $item['plus_diff'] = $item['plus_card_amount'] - ($prev['plus_card_amount'] ?? 0);
                $item['total_diff'] = $item['total_amount'] - ($prev['total_amount'] ?? 0);

                return $item;
            });

            return $final->sortByDesc('total_amount')->take(10)->values();
        });




        $topCitiesBranches = Cache::remember('topCitiesBranchese_' . $start_date."_".$user_id.(isset($_GET['reset']) ? rand(1,99999) :''), 60 * 60, function () use ($branchID, $start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date, $branchKode) {
            $topcitiesCar = Expertise::join('branches', 'branches.id', '=', 'expertises.branch_id')
                ->join('zone_branches', 'zone_branches.branch_id', '=', 'branches.id')
                ->join('zones', 'zones.id', '=', 'zone_branches.zone_id')
                ->where('zones.status', '!=', -1)
                ->where('zone_branches.deleted_at', null)
                ->where('expertises.deleted_at', null)
                ->where('expertises.manuel_save', 1)
                ->whereBetween('expertises.created_at', [$start_date, $finish_date])
                ->groupBy('zones.name')
                ->orderByDesc(DB::raw('COUNT(expertises.id)'))
                ->select('zones.name as title', DB::raw('COUNT(expertises.id) as car_count'))
                ->get();


//            $topcitiesCar = Car::when(!empty($branchID), function ($query) use ($branchID) {
//                return $query->whereIn('cars.branch_id', $branchID);
//            })
//                ->join('branches', 'cars.branch_id', '=', 'branches.id')
//                ->join('zone_branches', 'branches.id', '=', 'zone_branches.branch_id')
//                ->join('zones', 'zones.id', '=', 'zone_branches.zone_id')
//                ->where('zones.status', '!=', -1)
//                ->where('zone_branches.deleted_at', null)
//                ->whereBetween('cars.created_at', [$start_date, $finish_date])
//                ->where('UQ', null)
//                ->groupBy('zones.name')
//                ->orderByDesc(DB::raw('COUNT(cars.id)'))
////                ->limit(10)
//                ->select('zones.name as title', DB::raw('COUNT(cars.id) as car_count'))
//                ->get();
            if (!env('APP_LOCAL')) {
                /*$topcitiesCar_query2 = T202ARACLAR::join('T400_SRVBASLIK','T400_SRVBASLIK.T400_arac_UQ','=','T202_ARACLAR.T202_UQ')
                ->join('T103_SUBELER', 'T103_SUBELER.T103_kod', '=', 'T400_SRVBASLIK.T400_subekodu')
                    ->select('T103_SUBELER.T103_il',DB::raw('COUNT(T202_ARACLAR.T202_ID) as car_count'))
                    ->whereHas('t400srvbaslik', function ($query) use ($branchKode, $start_date, $finish_date) {
                        $query->whereBetween('T400_belgetarihi', [$start_date, $finish_date]);
                        if (!empty($branchKode)) {
                            $query->whereIn('T400_subekodu', $branchKode);
                        }
                        return $query;
                    })
                    ->groupBy('T103_SUBELER.T103_il')
                    ->orderByDesc(DB::raw('COUNT(T202_ARACLAR.T202_ID)'))
                    ->limit(10)
                    ->get();*/
                $topcitiesCar_query2 = collect();
            } else {
                $topcitiesCar_query2 = collect();
            }
            if (!empty($topcitiesCar_query2)) {
                foreach ($topcitiesCar_query2 as $topcitiesCar_query_old) {
                    $matchingBranch = $topcitiesCar->firstWhere('title', $topcitiesCar_query_old->il);
                    if ($matchingBranch) {
                        $matchingBranch->car_count += $topcitiesCar_query_old->car_count;
                    } else {
                        $topcitiesCar->push($topcitiesCar_query_old);
                    }
                }
                $combinedResults = $topcitiesCar;
            } else {
                $combinedResults = $topcitiesCar;
            }
            $sortedResults = $combinedResults->sortByDesc('count')->values();
            return $sortedResults;
        });


        $topCitiesBranchesCiro = Cache::remember('topCitiesBranchesCiros_'.$start_date."_".$finish_date."_".$user_id.(isset($_GET['reset']) ? rand(1,99999) :''), 60 * 60, function () use ($branchID, $start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date) {
            return Expertise::select('zones.name as title')
                ->join('branches', 'branches.id', '=', 'expertises.branch_id')
                ->join('zone_branches', 'branches.id', '=', 'zone_branches.branch_id')
                ->join('zones', 'zones.id', '=', 'zone_branches.zone_id')
                ->where('zones.status', '!=', -1)
                ->where('zone_branches.deleted_at', null)
                ->whereNull('expertise_payments.deleted_at')
                ->join('expertise_payments', 'expertises.id', '=', 'expertise_payments.expertise_id')
                ->when(!empty($branchID), function ($query) use ($branchID) {
                    return $query->whereIn('expertises.branch_id', $branchID);
                })
                ->whereBetween('expertises.created_at', [$start_date, $finish_date])
                ->selectRaw('SUM(expertise_payments.amount) as total_amount')
                ->groupBy('zones.name')
                ->orderByDesc('total_amount')
//                ->limit(10)
                ->get();
        });

        // $topTickets = Cache::remember('topTicketse_' . $start_date."_".$user_id.(isset($_GET['reset']) ? rand(1,99999) :''), 60 * 60, function () use ($branchID, $start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date) {
        //     return Ticket::select('status', 'subject', 'uuid')
        //         ->when(!empty($branchID), function ($query) use ($branchID) {
        //             return $query->whereIn('branch_id', $branchID);
        //         })
        //         ->whereBetween('created_at', [$start_date, $finish_date])
        //         ->orderByDesc('created_at')
        //         ->limit(10)
        //         ->get();
        // });
        $topTickets = Cache::remember('topTicketse_' . $start_date . "_" . $user_id . (isset($_GET['reset']) ? rand(1, 99999) : ''), 60 * 60, function () use ($branchID, $start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date) {
            return Complaint::selectRaw('talep_no, status, baslik, expertise_uuid, talep_nedeni, LEFT(musteri_ifadesi, 40) as musteri_ifadesi')
                ->when(!empty($branchID), function ($query) use ($branchID) {
                    return $query->whereIn('branch_id', $branchID);
                })
                ->whereBetween('created_at', [$start_date, $finish_date])
                ->orderByDesc('created_at')
                ->limit(10)
                ->get();
        });

        /* //Eski Plus Card Rapor Başlangıç
        $plus_card_total = PlusCardCrediAndPuanAdd::whereBetween('created_at', [$start_date, $finish_date]);
        if (!empty($branchID)) {
            $plus_card_total = $plus_card_total->whereHas('getUser', function ($query) use ($branchID) {
                $query->whereIn('branch_id', $branchID);
            });
        }
        $plus_card_total = $plus_card_total->where('balance_type', 'credits');

        $plus_card_total_previous = PlusCardCrediAndPuanAdd::whereBetween('created_at', [$previous_period_start_date, $previous_period_finish_date]);
        if (!empty($branchID)) {
            $plus_card_total_previous = $plus_card_total_previous->whereHas('getUser', function ($query) use ($branchID) {
                $query->whereIn('branch_id', $branchID);
            });
        }
        $plus_card_total_previous = $plus_card_total_previous->where('balance_type', 'credits');


        $plus_card_total_all = PlusCardCrediAndPuanAdd::query();
        if (!empty($branchID)) {
            $plus_card_total_all = $plus_card_total_all->whereHas('getUser', function ($query) use ($branchID) {
                $query->whereIn('branch_id', $branchID);
            });
        }
        $plus_card_total_all = $plus_card_total_all->where('balance_type', 'credits');

        $used_plus_card = ExpertisePayment::join('plus_card_credi_and_puan_add', 'plus_card_credi_and_puan_add.id', '=', 'expertise_payments.plus_card_odeme_id')
            ->where('expertise_payments.type', '=', 'plus_kart')
            ->where('plus_card_credi_and_puan_add.balance_type', '=', 'credits')
            ->where('plus_card_credi_and_puan_add.credi', '>', '0')
            ->count();

        $used_plus_card_amount = ExpertisePayment::select('plus_card_credi_and_puan_add.unit_price')->join('plus_card_credi_and_puan_add', 'plus_card_credi_and_puan_add.id', '=', 'expertise_payments.plus_card_odeme_id')
            ->where('expertise_payments.type', '=', 'plus_kart')
            ->where('plus_card_credi_and_puan_add.balance_type', '=', 'credits')
            ->where('plus_card_credi_and_puan_add.credi', '>', '0')
            ->sum('plus_card_credi_and_puan_add.unit_price');

        $used_Plus_card_credi_date = ExpertisePayment::select('plus_card_credi_and_puan_add.unit_price')->join('plus_card_credi_and_puan_add', 'plus_card_credi_and_puan_add.id', '=', 'expertise_payments.plus_card_odeme_id')
            ->where('expertise_payments.type', '=', 'plus_kart')
            ->where('plus_card_credi_and_puan_add.balance_type', '=', 'credits')
            ->where('plus_card_credi_and_puan_add.credi', '>', '0')
            ->whereBetween('expertise_payments.created_at', [$start_date, $finish_date])
            ->count();

        $used_Plus_card_amount_date = ExpertisePayment::select('plus_card_credi_and_puan_add.unit_price')->join('plus_card_credi_and_puan_add', 'plus_card_credi_and_puan_add.id', '=', 'expertise_payments.plus_card_odeme_id')
            ->where('expertise_payments.type', '=', 'plus_kart')
            ->where('plus_card_credi_and_puan_add.balance_type', '=', 'credits')
            ->where('plus_card_credi_and_puan_add.credi', '>', '0')
            ->whereBetween('expertise_payments.created_at', [$start_date, $finish_date])
            ->sum('plus_card_credi_and_puan_add.unit_price');

         $plus_card_array = array(
            'used_date_total_credi_count' => $used_Plus_card_credi_date,
            'used_date_total_credi_amount_count' => $used_Plus_card_amount_date,
            'date_total_credi_sales_count' => $plus_card_total->sum('credi'),
            'date_total_credi_sales_amount' => $plus_card_total->sum('credits_amount'),
            'total_credi_sales_count' => ($plus_card_total_all->sum('credi') - $used_plus_card),
            'total_credi_sales_amount' => ($plus_card_total_all->sum('odenen_kdv_dahil_fiyat') - $used_plus_card_amount),
            'previous_date_total_credi_sales_count' => count($plus_card_total_previous->get()),
            'previous_date_total_credi_sales_amount' => $plus_card_total_previous->sum('odenen_kdv_dahil_fiyat'),
        );

        // Eski Plus Card Rapor Bitiş
        */

        // Plus Card Report
        $plus_card_array = $this->plusCardReport($start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date, $branchID);

        $top_branches = Cache::remember('tops_branche_' . $start_date."_".$user_id.(isset($_GET['reset']) ? rand(1,99999) :''), 60 * 60, function () use ($branchID, $start_date, $finish_date, $branchKode) {
            $previousStart = $start_date->copy()->subWeek()->startOfDay();
            $previousEnd = $finish_date->copy()->subWeek()->endOfDay();

            $dashboardService = app(\App\Services\DashboardService::class);

            $current = $dashboardService->topCarCount([
                'branchId' => $branchID,
                'startDate' => $start_date,
                'endDate' => $finish_date,
            ]);

            $previous = $dashboardService->topCarCount([
                'branchId' => $branchID,
                'startDate' => $previousStart,
                'endDate' => $previousEnd,
            ])->keyBy('id');

            $final = $current->map(function ($item) use ($previous) {
                $prev = $previous[$item->id] ?? null;

                $item->bireysel_diff = $item->bireysel_count - ($prev->bireysel_count ?? 0);
                $item->plus_diff = $item->plus_card_count - ($prev->plus_card_count ?? 0);
                $item->total_diff = $item->car_count - ($prev->car_count ?? 0);

                $item->bireysel_class = $item->bireysel_diff > 0 ? 'text-success' : ($item->bireysel_diff < 0 ? 'text-danger' : 'text-dark');
                $item->plus_class = $item->plus_diff > 0 ? 'text-success' : ($item->plus_diff < 0 ? 'text-danger' : 'text-dark');
                $item->total_class = $item->total_diff > 0 ? 'text-success' : ($item->total_diff < 0 ? 'text-danger' : 'text-dark');

                return $item;
            });
            return $final->sortByDesc('car_count')->take(10)->values();
        });


        return response()->json([
            'success' => 'true',
            'type' => $request->type,
            'start_date' => $start_date->format('d.m.Y'),
            'finish_date' => $finish_date->format('d.m.Y'),
            'previous_period_start_date' => $previous_period_start_date->format('d.m.Y'),
            'previous_period_finish_date' => $previous_period_finish_date->format('d.m.Y'),
            'totalCarCount' => $totalsCarCount,
            'totalCiroCount' => $totalCiroData,
            'top_branches' => $top_branches,
            'top_branches_ciro' => $top_branches_ciro,
            'totalsCitiesCarCount' => $topCitiesBranches,
            'topCitiesCiro' => $topCitiesBranchesCiro,
            'topTickets' => $topTickets,
            'plus_card_array' => $plus_card_array,
            'totalCiro_old' => $totalCiro_old,
        ]);
    }

    /**
     * Plus Card Report
     *
     * @param $start_date
     * @param $finish_date
     * @param $previous_period_start_date
     * @param $previous_period_finish_date
     * @param $branchID
     * @return array
     */
    public function plusCardReport($start_date, $finish_date, $previous_period_start_date, $previous_period_finish_date, $branchID)
    {
        /** @var User $authUser */
        $authUser = Auth()->user();

        // Plus card payments
        $plusCardExpensesQuery = ExpertisePayment::query();
        $plusCardExpensesQuery = $plusCardExpensesQuery->where('type', 'plus_kart')
            ->join('expertises', 'expertises.id', '=', 'expertise_payments.expertise_id')
            ->select('expertise_payments.*', 'expertises.created_at as e_created_at')
            ->whereBetween('expertises.created_at', [$start_date, $finish_date])
            ->whereNotNull('expertise_payments.plus_card_id');

        // If the user is not an admin, filter by branch ID
        if (!$authUser->isAdmin()) {
            $plusCardExpensesQuery->whereIn('expertises.branch_id', $branchID);
        }

        // Total plus card payments
        $usedPlusCardCreditCount = $plusCardExpensesQuery->count();

        // Total plus card payments amount
        $usedPlusCardAmountSum = $plusCardExpensesQuery->sum('expertise_payments.amount');

        // Plus card sales
        $plusCardTotalQuery = PlusCardCrediAndPuanAdd::query();

        // If the user is not an admin, filter by branch ID
        if (!$authUser->isAdmin()) {
            $plusCardTotalQuery->whereHas('getPlusCard', function ($query) use ($branchID) {
                $query->whereIn('branch_id', $branchID);
            });
        }

        $plusCardTotalQuery = $plusCardTotalQuery->where('balance_type', 'credits')
            ->whereNull('devir_miktar');

        $plusCardTotalInDateRange = clone $plusCardTotalQuery;
        $plusCardTotalInDateRange = $plusCardTotalInDateRange->whereBetween('created_at', [$start_date, $finish_date]);

        $plusCardTotalInPreviousDateRange = clone $plusCardTotalQuery;
        $plusCardTotalInPreviousDateRange = $plusCardTotalInPreviousDateRange->whereBetween('created_at', [$previous_period_start_date, $previous_period_finish_date]);

        $plusCardTotalInDateRange = $plusCardTotalInDateRange->where(function ($query) {
            return $query->where('payment_type', 'banka')->orWhere('payment_type', 'kredi_karti')->orWhere('payment_type', 'nakit');
        });

        $plusCardTotalInPreviousDateRange = $plusCardTotalInPreviousDateRange->where(function ($query) {
            return $query->where('payment_type', 'banka')->orWhere('payment_type', 'kredi_karti')->orWhere('payment_type', 'nakit');
        });

        return [
            'used_date_total_credi_count' => $usedPlusCardCreditCount,
            'used_date_total_credi_amount_count' => $usedPlusCardAmountSum,
            'date_total_credi_sales_count' => $plusCardTotalInDateRange->sum('credi'),
            'date_total_credi_sales_amount' => $plusCardTotalInDateRange->sum('odenen_kdv_dahil_fiyat'),
            'total_credi_sales_count' => $plusCardTotalQuery->sum('credi'),
            'total_credi_sales_amount' => $plusCardTotalQuery->sum('odenen_kdv_dahil_fiyat'),
            'previous_date_total_credi_sales_count' => $plusCardTotalInPreviousDateRange->sum('credi'),
            'previous_date_total_credi_sales_amount' => $plusCardTotalInPreviousDateRange->sum('odenen_kdv_dahil_fiyat'),
        ];
    }

    public function mostIncomeBranches(Request $request, DashboardService $dashboardService)
    {
        $startDate = isset($_GET['start_date']) ? Carbon::make($_GET['start_date'])->startOfDay() : Carbon::now()->startOfDay();
        $endDate = isset($_GET['end_date']) ? Carbon::make($_GET['end_date'])->endOfDay() : Carbon::now()->endOfDay();
        $user = Auth()->user();
        $user_id = $user->id;


        $branchIDs = $user->getBranchIds();

        // Previous week range
        $previousStart = $startDate->copy()->subWeek()->startOfDay();
        $previousEnd = $endDate->copy()->subWeek()->endOfDay();

        // Fetch current data
        $current = $dashboardService->mostIncomeBranches([
            'branchIds' => $branchIDs,
            'startDate' => $startDate,
            'endDate' => $endDate,
        ]);

        // Fetch previous data
        $previous = $dashboardService->mostIncomeBranches([
            'branchIds' => $branchIDs,
            'startDate' => $previousStart,
            'endDate' => $previousEnd,
        ])->keyBy('branch_id');
        // The $expertise_query_2 commented not used anymore also condition is always true And it combines empty object, so  I removed it
        // Calculate diffs
        $sortedResults = $current->map(function ($item) use ($previous) {
            $prev = $previous[$item['branch_id']] ?? null;

            $item['bireysel_diff'] = $item['bireysel_amount'] - ($prev['bireysel_amount'] ?? 0);
            $item['plus_diff'] = $item['plus_card_amount'] - ($prev['plus_card_amount'] ?? 0);
            $item['total_diff'] = ($item['bireysel_diff'] ?? 0) + ($item['plus_diff'] ?? 0);

            return $item;
        })->sortByDesc('total_amount')->values();

        return view('pages.most_income_branches', compact('sortedResults','startDate','endDate'));
    }

    /**
     * Get the top branch cars
     *
     * @param Request $request
     * @param DashboardService $dashboardService
     * @return View
     */
    public function topBranchCars(Request $request, DashboardService $dashboardService)
    {
        /** @var User $user */
        $user = auth()->user();

        $startDate = $request->get('start_date', null)
            ? Carbon::make($request->get('start_date'))->startOfDay() : Carbon::now()->startOfDay();
        $endDate = $request->get('end_date', null)
            ? Carbon::make($request->get('end_date'))->endOfDay() : Carbon::now()->endOfDay();

        $branchID = $user->getBranchIds();

        $previousStart = $startDate->copy()->subWeek()->startOfDay();// one week ago of the given start date and end date
        $previousEnd = $endDate->copy()->subWeek()->endOfDay();

        $current = $dashboardService->topCarCount([
            'branchId' => $branchID,
            'startDate' => $startDate,
            'endDate' => $endDate,
            'withZoneAndCity' => true,
        ]);

        $previous = $dashboardService->topCarCount([
            'branchId' => $branchID,
            'startDate' => $previousStart,
            'endDate' => $previousEnd,
            'withZoneAndCity' => false,
        ]);
        $previousById = $previous->keyBy('id'); // Key by id for easy access to the index of collection

        $branches = $current->map(function ($item) use ($previousById) {
            $prev = $previousById[$item->id] ?? null;
            // Total difference
            $item->change_amount = $item->car_count - ($prev->car_count ?? 0);
            // Bireysel difference
            $item->bireysel_diff = $item->bireysel_count - ($prev->bireysel_count ?? 0);

            // Plus Card difference
            $item->plus_diff = $item->plus_card_count - ($prev->plus_card_count ?? 0);
            return $item;
        });

        return view(
            'pages.dashboard.top_branch_cars',
            compact('branches', 'startDate', 'endDate')
        );
    }
}
