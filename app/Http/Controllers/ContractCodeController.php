<?php

namespace App\Http\Controllers;

use Illuminate\Http\Request;
use App\Services\ContractCodeService;
use App\Models\ContractCode;

/**
 * Class ContractCodeController
 *
 * @package App\Http\Controllers
 */
class ContractCodeController extends Controller
{
    /**
     * Fetch contract's codes table
     */
    public function indexAjax(Request $request, ContractCodeService $contractCodeService)
    {
        return response()->json($contractCodeService->getContractCodes($request));
    }

    public function destroy(ContractCode $contractCode)
    {
        if ($contractCode->used == 0) {
            $contractCode->delete();
            return redirect()->back()->with('success', 'Başarıyla Silindi');
        } else {
            return redirect()->back()->with('error', '<PERSON><PERSON>llan<PERSON>lm<PERSON>');
        }
    }
}
