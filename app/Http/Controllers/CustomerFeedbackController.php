<?php

namespace App\Http\Controllers;

use App\Models\CustomerFeedback;
use Illuminate\Http\Request;

class CustomerFeedbackController extends Controller
{
    /**
     * Display a listing of the resource.
     */
    public function index()
    {
        $items = CustomerFeedback::all();
        return view('pages.customer_feedback.index',['items'=>$items]);
    }

    /**
     * Show the form for creating a new resource.
     */
    public function create()
    {
        return view('customer.feedback.create');
    }

    /**
     * Store a newly created resource in storage.
     */
    public function store(Request $request)
    {
        if (!$request->hasFile('image'))
            return back()->with('error','Dosya Seçmediniz');

        $request->validate([
            'image' => 'required|image|mimes:jpeg,png,jpg,gif|max:2048', // Adjust max file size as needed
        ]);

        $customerFeedback = new CustomerFeedback();
        $customerFeedback->customer_id = auth('customer')->user()->id;
        $customerFeedback->image = $request->file('image')->store('customer_feedback','public2');
        $customerFeedback->message = $request->message;
        $customerFeedback->status = 2;
        $customerFeedback->save();

        return  redirect()->route('index')->with('success','Talebiniz Alınmıştır.');
    }

    /**
     * Display the specified resource.
     */
    public function show(CustomerFeedback $customerFeedback)
    {
        //
    }

    /**
     * Show the form for editing the specified resource.
     */
    public function edit(CustomerFeedback $customerFeedback)
    {
        return view('pages.customer_feedback.edit',['customerFeedback'=>$customerFeedback]);
    }

    /**
     * Update the specified resource in storage.
     */
    public function update(Request $request, CustomerFeedback $customerFeedback)
    {
        if ($request->status != $customerFeedback->status)
            logRecord("edit_customer_feedback",auth()->user()->name . " adlı kullanıcı $customerFeedback->id id'li çek gönder talebin durumunu $customerFeedback->status => $request->status olarak güncelledi.",$customerFeedback->id);
        $customerFeedback->status = $request->status;
        $customerFeedback->save();

        return redirect()->route('customer-feedbacks.index')->with('success','Başarıyla Güncellendi');
    }

    /**
     * Remove the specified resource from storage.
     */
    public function destroy(CustomerFeedback $customerFeedback)
    {
        $customerFeedback->delete();

        logRecord("delete_customer_feedback",auth()->user()->name . " adlı kullanıcı $customerFeedback->id id'li çek gönder talebini sildi.",$customerFeedback->id);
        return redirect()->route('customer-feedbacks.index')->with('success','Başarıyla Silindi');
    }
}
