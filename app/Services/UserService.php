<?php

namespace App\Services;

use App\Models\User;
use App\Models\UserSavedFilter;
use App\Models\ZoneBranch;
use Illuminate\Http\Request;

/**
 * Class UserService
 */
class UserService
{
    /**
     * This method returns filtered users
     *
     * @param Request $request
     * @param array $authUserBranchIds
     * @param User $authUser
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function getFilteredUsers(Request $request, $authUserBranchIds, $authUser, $all = false)
    {
        $limit = $request->input('length', 10);
        $start = $request->input('start', 0);
        $searchValue = $request->input('search')['value'] ?? null; // Get the search input

        $items = User::query()
            ->select(
                'users.name', // I put the table name to prevent the ambiguity
                'users.user_role_group_id',
                'users.gender',
                'users.second_name',
                'users.surname',
                'users.email',
                'users.tc',
                'users.telephone',
                'users.second_telephone',
                'users.id',
                'users.department',
                'user_role_groups.name as position_name',
                'users.branch_id',
                'users.zone_id',
                'users.status',
                'users.dogum_tarihi',
                'users.work_start_date',
                'users.alt_beden',
                'users.ust_beden',
                'users.ayakkabi_numarasi',
                'branches.kisa_ad as branch_name',
                'branch_categories.name as branch_category_name',
                'zones.name as zone_name'
            )
            ->leftJoin('branches', 'users.branch_id', '=', 'branches.id')
            ->leftJoin('branch_categories', 'branches.category_id', '=', 'branch_categories.id')
            ->leftJoin('zones', 'users.zone_id', '=', 'zones.id')
                ->leftJoin('user_role_groups', 'users.user_role_group_id', '=', 'user_role_groups.id')
                ->leftJoin('user_branches2', 'users.id', '=', 'user_branches2.user_id')
            ->orderBy('users.status', 'desc')
            ->orderBy('users.created_at', 'desc');

        $filters = $request->only([
            'name',
            'second_name',
            'surname',
            'telephone',
            'eposta',
            'tc',
            'gender',
            'alt_beden',
            'ayakkabi_numarasi',
            'blood_group',
            'status',
            'user_role_group_id',
            'branch_id',
            'zone_id',
            'saved_filter_id'
        ]);

        $selectedSavedFilter = null;
        if (!empty($filters['saved_filter_id'])) {
            $selectedSavedFilter = UserSavedFilter::where(['user_id' => auth()->user()->id, 'id' => (int)$filters['saved_filter_id']])->first();
        }

        if (!empty($selectedSavedFilter)) {
            $filters = array_merge($filters, optional($selectedSavedFilter)->only([
                'name',
                'second_name',
                'surname',
                'telephone',
                'eposta',
                'tc',
                'gender',
                'alt_beden',
                'ayakkabi_numarasi',
                'blood_group',
                'status',
                'user_role_group_id',
                'branch_id',
                'zone_id',
            ]));
        }

        if (!$authUser->isAdmin()) {
            $items->whereHas('branches', function ($query) use ($authUserBranchIds) {
                $query->whereIn('branch_id', $authUserBranchIds)->whereNull('deleted_at');
            });

            $items->where('type', '!=', 'admin');
        }

        foreach ($filters as $key => $value) {
            if ($value == '0' || !empty($value)) {
                switch ($key) {
                    case 'name':
                    case 'second_name':
                    case 'surname':
                    case 'tc':
                        $items->where("users.$key", 'LIKE', "%{$value}%");
                        break;
                    case 'eposta':
                        $items->where("users.email", 'LIKE', "%{$value}%");
                        break;
                    case 'telephone':
                        $tel = str_replace(['(', ')', ' '], '', $value);
                        $items = $items->where(function ($query) use ($tel) {
                            return $query->where('telephone', 'like', '%' . $tel . '%')
                                ->orWhere('second_telephone', 'like', '%' . $tel . '%');
                        });
                        break;
                    case 'gender':
                    case 'alt_beden':
                    case 'ayakkabi_numarasi':
                    case 'blood_group':
                    case 'user_role_group_id':
                    case 'status':
                        $items->where("users.$key", $value);
                        if ($value == '0') {
                            $items->where('users.' . $key, $value)->reorder()->orderBy('users.status', 'desc')->orderBy('users.updated_at', 'desc');
                        } else {
                            $items->where('users.' . $key, $value);
                        }

                        break;
                    case 'branch_id':
                        if ($filters['branch_id'] == '0') {
                            break;
                        }

                        // If the user is an admin, then the user can see umran plus users
                        if ($authUser->isAdmin() && $value == 26) {
                            $items->whereHas('branches', function ($query) use ($value) {
                                $query->where('branches.id', $value)->whereNull('deleted_at');
                            });
                        } elseif (in_array($value, $authUserBranchIds)) {
                            // If the user is not an admin, then the user can only see the users in his/her own branch
                            $items->whereHas('branches', function ($query) use ($value) {
                                $query->where('branches.id', $value)->whereNull('deleted_at');
                            });

                            // We don't want to show the users with the role of "Admin" in the user list
                            $items->where('users.type', '!=', 'admin');
                        }

                        break;
                    case 'zone_id':
                        if (!empty($value) && $authUser->isAdmin() && $filters['zone_id'] != '0') {
                            $zoneBranches = ZoneBranch::where('zone_id', $value)->pluck('branch_id')->toArray();
                            $items->whereIn('user_branches2.branch_id', $zoneBranches);
                        }
                        break;
                }
            }
        }

        // Apply search filter
        if (!empty($searchValue)) {
            $items->where(function ($query) use ($searchValue) {
                $query->whereRaw("CONCAT_WS(' ', users.name, users.second_name, users.surname) LIKE ?", ["%{$searchValue}%"]) // Search Full Name
                    ->orWhere('branches.kisa_ad', 'LIKE', "%{$searchValue}%") // Search Branch Name
                    ->orWhere('zones.name', 'LIKE', "%{$searchValue}%") // Search Bölge
                    ->orWhere('users.department', 'LIKE', "%{$searchValue}%") // Search Department
                    ->orWhere('user_role_groups.name', 'LIKE', "%{$searchValue}%"); // Search Görev
            });
        }

        // Get total record count before pagination
        $totalRecords = $items->count();
        if ($all == false) {
            $items = $items->offset($start)->limit($limit);
        }

        return ['total' => $totalRecords, 'users' => $items->get(), 'filters' => $filters];
    }

    /**
     * This method updates the work clothes of the user
     *
     * @param Request $request
     * @param User $user
     * @return void
     */
    public function updateWorkClothes(Request $request, User $user)
    {
        $log = "";
        $hasUpdates = false;

        if ($request->has('alt_beden') && $request->alt_beden != $user->alt_beden) {
            $log .= "alt bedenini {$user->alt_beden} => {$request->alt_beden} olarak güncelledi.";
            $user->alt_beden = $request->alt_beden;
            $hasUpdates = true;
        }

        // Üst Beden
        if ($request->has('ust_beden') && $request->ust_beden != $user->ust_beden) {
            $log .= "üst bedenini {$user->ust_beden} => {$request->ust_beden} olarak güncelledi.";
            $user->ust_beden = $request->ust_beden;
            $hasUpdates = true;
        }

        // Ayakkabı Numarası
        if ($request->has('ayakkabi_numarasi') && $request->ayakkabi_numarasi != $user->ayakkabi_numarasi) {
            $log .= "ayakkabı numarasını {$user->ayakkabi_numarasi} => {$request->ayakkabi_numarasi} olarak güncelledi.";
            $user->ayakkabi_numarasi = (int)$request->ayakkabi_numarasi;
            $hasUpdates = true;
        }

        if ($hasUpdates) {
            $user->save();

            logRecord(
                "edit_user",
                auth()->user()->name . " adlı kullanıcı $user->name adlı kullanıcının " . $log . " güncelledi.",
                $user->id
            );
        }
    }
}
