<?php

namespace App\Services;
use App\Models\Expertise;

use App\Models\Branch;

/**
 * Class DashboardService
 *
 * @package Dashboard
 */
class DashboardService
{
    /**
     * Get top count of branch
     *
     * @param array $params
     * @return mixed
     */
    public function topCarCount(array $params): mixed
    {
        $user = auth()->user();

        // Admin kullanıcılar için branch_id, bayi kullanıcıları için kayit_branch_id kullanılır
        if ($user->isAdmin()) {
            $branches = Branch::leftJoin('expertises', function ($join) use ($params) {
                $join->on('branches.id', '=', 'expertises.branch_id')
                    ->where('expertises.manuel_save', 1)
                    ->whereNull('expertises.deleted_at')
                    ->whereBetween('expertises.created_at', [$params['startDate'], $params['endDate']]);
            })
            ->leftJoin('expertise_payments', 'expertises.id', '=', 'expertise_payments.expertise_id')
            ->whereNull('expertise_payments.deleted_at')
            ->when(!empty($params['branchId']), function ($query) use ($params) {
                return $query->whereIn('branches.id', $params['branchId']);
            })->where('branches.status', 1)
            ->selectRaw('
                branches.id,
                branches.kisa_ad,
                COUNT(CASE WHEN expertise_payments.type IN ("plus_card", "plus_kart") THEN expertises.id END) AS plus_card_count,
                COUNT(CASE WHEN expertise_payments.type NOT IN ("plus_card", "plus_kart") THEN expertises.id END) AS bireysel_count,
                COUNT(expertises.id) AS car_count
            ')
            ->groupBy('branches.id', 'branches.kisa_ad')
            ->orderByDesc('car_count')
            ->get();
        } else {
            // Bayi kullanıcıları için kayit_branch_id kullanılır
            $branches = Branch::leftJoin('expertises', function ($join) use ($params) {
                $join->on('branches.id', '=', 'expertises.kayit_branch_id')
                    ->where('expertises.manuel_save', 1)
                    ->whereNull('expertises.deleted_at')
                    ->whereBetween('expertises.created_at', [$params['startDate'], $params['endDate']]);
            })
            ->leftJoin('expertise_payments', 'expertises.id', '=', 'expertise_payments.expertise_id')
            ->whereNull('expertise_payments.deleted_at')
            ->when(!empty($params['branchId']), function ($query) use ($params) {
                return $query->whereIn('branches.id', $params['branchId']);
            })->where('branches.status', 1)
            ->selectRaw('
                branches.id,
                branches.kisa_ad,
                COUNT(CASE WHEN expertise_payments.type IN ("plus_card", "plus_kart") THEN expertises.id END) AS plus_card_count,
                COUNT(CASE WHEN expertise_payments.type NOT IN ("plus_card", "plus_kart") THEN expertises.id END) AS bireysel_count,
                COUNT(expertises.id) AS car_count
            ')
            ->groupBy('branches.id', 'branches.kisa_ad')
            ->orderByDesc('car_count')
            ->get();
        }
        // Add zone_name and il_name without N+1 situation
        if (!empty($params['withZoneAndCity'])) {
            // Eager load the zones and city relationships
            $branchModels = Branch::with(['zones', 'getCity'])
                ->whereIn('id', $branches->pluck('id'))
                ->get()
                ->keyBy('id'); // Key by branch ID for faster access

            return $branches->map(function ($branch) use ($branchModels) {
                $model = $branchModels[$branch->id] ?? null;
                $branch->zone_name = optional($model?->zones->first())->name ?? '';
                $branch->il_name = optional($model?->getCity)->title ?? '';
                return $branch;
            });
        }

        return $branches;
    }


    public function mostIncomeBranches(array $params):mixed
    {
        $branchIDs = $params['branchIds'] ?? [];
        $startDate = $params['startDate'];
        $endDate = $params['endDate'];

        $query = Expertise::when(!empty($branchIDs), fn($q) => $q->whereIn('branch_id', $branchIDs))
            ->join('branches', 'branches.id', '=', 'expertises.branch_id')
            ->join('expertise_payments', 'expertises.id', '=', 'expertise_payments.expertise_id')
            ->whereBetween('expertises.created_at', [$startDate, $endDate])
            ->whereNull('expertises.deleted_at')
            ->whereNull('expertise_payments.deleted_at')
            ->where('expertises.manuel_save', 1)
            ->selectRaw('
                branches.id as branch_id,
                branches.kisa_ad as unvan,
                SUM(CASE WHEN expertise_payments.type IN ("plus_card", "plus_kart") THEN expertise_payments.amount ELSE 0 END) AS plus_card_amount,
                SUM(CASE WHEN expertise_payments.type NOT IN ("plus_card", "plus_kart") THEN expertise_payments.amount ELSE 0 END) AS bireysel_amount,
                SUM(expertise_payments.amount) as total_amount
            ')
            ->groupBy('branches.id', 'branches.kisa_ad', 'branches.belge_kod')
            ->orderByDesc('total_amount')
            ->get();

        // Load zone and city (eager loading without N+1)
        $branches = Branch::with(['zones', 'getCity'])
            ->whereIn('id', $query->pluck('branch_id'))
            ->get()
            ->keyBy('id');

        return $query->map(function ($item) use ($branches) {
            $branch = $branches[$item->branch_id] ?? null;
            return [
                'branch_id' => $item->branch_id,
                'kisa_ad' => $item->unvan,
                'zone_name' => optional($branch?->zones->first())->name ?? '',
                'il_name' => optional($branch?->getCity)->title ?? '',
                'total_amount' => $item->total_amount,
                'plus_card_amount' => $item->plus_card_amount,
                'bireysel_amount' => $item->bireysel_amount,
            ];
        });
    }
}
