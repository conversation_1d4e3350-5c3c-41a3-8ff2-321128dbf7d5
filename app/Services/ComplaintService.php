<?php

namespace App\Services;

use App\Models\Complaint;
use Carbon\Carbon;
use Illuminate\Database\Eloquent\Collection;
use Illuminate\Http\Request;

/**
 * Service ComplaintService
 * @package Complaint
 */
class ComplaintService
{
    /**
     * Display a listing of the resource.
     *
     * @param Request $request
     * @return mixed
     */
    public function index(Request $request): Collection
    {
        if (auth('customer')->check()) {
            $items = Complaint::where('customer_id', auth('customer')->user()->id)->get();
        } else {
            $authUser = auth()->user();
            $startDate = isset($request['start_date']) && $request['start_date'] != ''
                ? Carbon::make($request['start_date'])->format('Y-m-d') : now()->subDays(7)->format('Y-m-d');
            $endDate = isset($request['end_date']) && $request['end_date'] != ''
                ? Carbon::make($request['end_date'])->format('Y-m-d') : now()->format('Y-m-d');
            $authUserBranchIds = $authUser->getBranchIds();

            $items = Complaint::whereDate('created_at', '>=', $startDate)
                ->whereDate('created_at', '<=', $endDate)
                ->when(isset($request['talep_no']) && $request['talep_no'] != '', function ($query) use ($request) {
                    return $query->where('talep_no', $request['talep_no']);
                })
                ->when(isset($request['ad_unvan']) && $request['ad_unvan'] != '', function ($query) use ($request) {
                    return $query->where('ad_unvan', 'like', '%' . $request['ad_unvan'] . '%');
                })
                ->when(isset($request['soyad']) && $request['soyad'] != '', function ($query) use ($request) {
                    return $query->where('soyad', 'like', '%' . $request['soyad'] . '%');
                })
                ->when(isset($request['telefon']) && $request['telefon'] != '', function ($query) use ($request) {
                    $phoneNumber = preg_replace('/[\(\)\s]+/', '', $request['telefon']);
                    return $query->where(function ($q) use ($phoneNumber) {
                        $q->where('telefon', 'like', '%' . $phoneNumber . '%')
                            ->orWhere('telefon', 'like', '%' . substr($phoneNumber, 1) . '%');
                    });
                })
                ->when(isset($request['talep_nedeni']) && $request['talep_nedeni'] != '', function ($query) use ($request) {
                    return $query->where('talep_nedeni', $request['talep_nedeni']);
                })
                ->when(isset($request['status']) && $request['status'] != '', function ($query) use ($request) {
                    return $query->where('status', $request['status']);
                })
                ->when($authUser->isAdmin(), function ($query) use ($request) {
                    return $query->when(isset($request['branch_id'])
                        && (int)$request['branch_id'] > 0, function ($query) use ($request) {
                        return $query->where('branch_id', (int)$request['branch_id']);
                    });
                }, function ($query) use ($authUserBranchIds, $request) {
                    return $query->when(isset($request['branch_id'])
                        && (int)$request['branch_id'] > 0, function ($query) use ($authUserBranchIds, $request) {
                        if (in_array((int)$request['branch_id'], $authUserBranchIds)) {
                            return $query->where('branch_id', (int)$request['branch_id']);
                        }
                    })->when(!isset($request['branch_id']), function ($query) use ($authUserBranchIds) {
                        return $query->whereIn('branch_id', $authUserBranchIds);
                    });
                })->get();
        }

        return $items;
    }

    public function create(Request $request)
    {
        $complaint = new Complaint();

        $branchId = 0;

        if ($request->get('expertiseUuid')) {
            $complaint->expertise_uuid = $request->get('expertiseUuid');
            $branchId = $request->get('expertiseBranchId');
        }

        $complaint->customer_id = $request->customer_id ?? 0;
        $complaint->branch_id = $branchId;
        $complaint->talep_no = rand(100000000000, ************);
        $complaint->ad_unvan = $request->unvan != null ? $request->unvan : $request->ad;
        $complaint->soyad = $request->soyad;
        $complaint->vergi_dairesi = $request->vergi_dairesi;
        $complaint->tc_vergi_no = ($request->tc != null ? $request->tc : $request->vergi_no) ?? '11111111111';
        $complaint->eposta = $request->email ?? '';
        $complaint->telefon = clearPhone($request->telephone);
        // We use il_id for right or unfair
        $complaint->il_id = 0;
        $complaint->ilce_id = $request->town_id ?? 0;
        $complaint->semt = $request->semt ?? 0;
        $complaint->mahalle = $request->mahalle ?? '';
        $complaint->talep_nedeni = $request->talep_nedeni;
        $complaint->musteri_ifadesi = $request->musteri_ifadesi;
        $complaint->save();
    }

    /**
     * Update result status
     *
     * @param Request $request
     * @param Complaint $complaint
     * @return void
     */
    public function updateResultStatus(Request $request, Complaint $complaint): void
    {
        $complaint->il_id = $request->get('resultStatus');
        $complaint->save();
    }
}
