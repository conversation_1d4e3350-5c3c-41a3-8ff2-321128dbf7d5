<?php

namespace App\Imports;

use App\Models\Branch;
use App\Models\Car;
use App\Models\Expertise;
use App\Models\QueryLog;
use App\Models\QueryLogUpdate;
use Illuminate\Support\Collection;
use Maatwebsite\Excel\Concerns\RemembersChunkOffset;
use Maatwebsite\Excel\Concerns\ToCollection;
use Illuminate\Support\Facades\DB;
use Maatwebsite\Excel\Concerns\WithChunkReading;
use Maatwebsite\Excel\Concerns\WithMultipleSheets;

class OtoSorguImport implements ToCollection, WithChunkReading
{
    use RemembersChunkOffset;


    /**
    * @param Collection $collection
    */
    public function collection(Collection $collection)
    {
        set_time_limit(0);
        foreach ($collection as $key => $c) {
            $query = DB::table('query_logs_yedek')->find((int)$c[0]);
            if ($query) {
                $updates = [];

                switch ($c[3]) {
                    case "Hasar Sorgu Plaka":
                    case "Hasar Sorgu Şasi":
                        if (is_null($query->hasar_ucret)) {
                            $updates = [
                                'hasar_ucret' => $c[8],
                                'hasar_komisyon' => 1,
                            ];
                        }
                        break;

                    case "Borç Sorgula":
                        if (is_null($query->borc_ucret)) {
                            $updates = [
                                'borc_ucret' => $c[8],
                                'borc_komisyon' => 1,
                            ];
                        }
                        break;

                    case "Kilometre Sorgu Plaka":
                    case "Kilometre Sorgu Şasi":
                        if (is_null($query->kilometre_ucret)) {
                            $updates = [
                                'kilometre_ucret' => $c[8],
                                'kilometre_komisyon' => 1,
                            ];
                        }
                        break;

                    case "Araç Detay Sorgu Plaka":
                    case "Araç Detay Sorgu Şasi":
                        if (is_null($query->detail_ucret)) {
                            $updates = [
                                'detail_ucret' => $c[8],
                                'detail_komisyon' => 1,
                            ];
                        }
                        break;

                    case "Değişen Parça Plaka":
                    case "Değişen Parça Şasi":
                        if (is_null($query->degisen_ucret)) {
                            $updates = [
                                'degisen_ucret' => $c[8],
                                'degisen_komisyon' => 0,
                            ];
                        }
                        break;
                }

                if (!empty($updates)) {
                    DB::table('query_logs_yedek')->where('id', (int)$c[0])->update($updates);
                }
            }











//
//            $cleaned = ltrim($c[6], '0');
//            $check_sorgu = QueryLogUpdate::where('otosorgu_id',$c[1])->first();
//
//
//            if(empty($check_sorgu)){
//                $query_log_query = Car::select('expertises.uuid')
//                    ->join('expertises', 'expertises.car_id', '=', 'cars.id')
//                    ->join('query_logs','query_logs.uuid','=','expertises.uuid')
//                    ->where(function ($query) use ($c) {
//                        $query->where('cars.plaka', 'LIKE', '%' . $c[4] . '%')
//                            ->orWhere('cars.sase_no', 'LIKE', '%' . $c[4] . '%');
//                    })
//                    ->first();
//                if ($query_log_query) {
//                    $query_log = QueryLog::where('uuid',$query_log_query->uuid)->first();
//                    if(!empty($query_log)){
//
//                        if ($c[3] == "Hasar Sorgu Plaka" || $c[3] == "Hasar Sorgu Şasi") {
//                            $hasar = json_decode($query_log->hasar);
//                            $hasar->data->amount = $c[5];
//                            $query_log->hasar = json_encode($hasar);
//                            $query_log->hasar_ucret = $c[5];
//                        } elseif ($c[3] == "Borç Sorgula") {
//                            $borc = json_decode($query_log->borc);
//                            $borc->data->amount = $c[5];
//                            $query_log->borc = json_encode($borc);
//                            $query_log->borc_ucret = $c[5];
//                        } elseif ($c[3] == "Kilometre Sorgu Plaka" || $c[3] == "Kilometre Sorgu Şasi") {
//                            $kilometre = json_decode($query_log->kilometre);
//                            $kilometre->data->amount = $c[5];
//                            $query_log->kilometre = json_encode($kilometre);
//                            $query_log->kilometre_ucret = $c[5];
//                        } elseif ($c[3] == "Araç Detay Sorgu Plaka" || $c[3] == "Araç Detay Sorgu Şasi") {
//                            $detail = json_decode($query_log->detail);
//                            $detail->data->amount = $c[5];
//                            $query_log->detail = json_encode($detail);
//                            $query_log->detail_ucret = $c[5];
//                        } elseif ($c[3] == "Değişen Parça Plaka" || $c[3] == "Değişen Parça Şasi") {
//                            $degisen = json_decode($query_log->degisen);
//                            $degisen->data->amount = $c[5];
//                            $query_log->degisen = json_encode($degisen);
//                            $query_log->degisen_ucret = $c[5];
//                        }
//
//                        // Güncellenmiş veriyi kaydet
//                        $query_log->save();
//
//                        // Veritabanına ekleme işlemini yap
//
//                        $update_query_log = new QueryLogUpdate();
//                        $update_query_log->query_log_id = $query_log->id;
//                        $update_query_log->type = $c[3];
//                        $update_query_log->ucret = $c[5];
//                        $update_query_log->search = $c[4];
//                        $update_query_log->otosorgu_id = $c[1];
//                        $update_query_log->user_id = $c[7];
//                        $update_query_log->save();
//                        dd($update_query_log);
//                    }
//                }
//            }

        }
    }

    public function chunkSize(): int
    {
        return 100;
    }
}
