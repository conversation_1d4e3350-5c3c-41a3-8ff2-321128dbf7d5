ALTER TABLE `expertises` ADD UNIQUE(`uuid`);
INSERT IGNORE INTO umramdb_backup.expertises (
                    id,old_id, uuid, user_id, kayit_branch_id, branch_id, cari_id, satici_id, alici_id, car_id, km, sase_no, km_type, net_agirlik, nereden_ulast<PERSON>z,
                    sorgu_hizmeti, belge_tarihi, belge_no, belge_ozel_kodu, sigorta_teklif_ver, yayin_yasagi, kayit_yeri, status, odeme_turu, bodywork_image,
                    diagnostic_file, arac_kontrol_user, arac_kontrol, fren_kontrol_user, fren_kontrol, kaporta_kontrol_user, kaporta_kontrol,
                    diagnostic_kontrol_user, diagnostic_kontrol, ic_kontrol_user, ic_kontrol, lastik_jant_kontrol_user, lastik_jant_kontrol,
                    alt_motor_kontrol_user, alt_motor_kontrol, komponent_kontrol_user, komponent_kontrol, co2_kontrol_user, co2_kontrol, hasar_sorgu_sonuc,
                    kilometre_sorgu_sonuc, borc_sorgu_sonuc, ruhsat_sorgu_sonuc, payment_type, plus_card_payment_type, plus_kart_id, audio_url,
                    cikis_tarihi, manuel_save, employee_downloaded, ftp_ok, ftp_date, created_at, updated_at, deleted_at
                )
SELECT
    dbtest_db.expertises.id + 1000000 as id,
    dbtest_db.expertises.old_id,
    dbtest_db.expertises.uuid,
    dbtest_db.expertises.user_id,
    dbtest_db.expertises.kayit_branch_id,
    dbtest_db.expertises.branch_id,
    dbtest_db.expertises.cari_id,
    dbtest_db.expertises.satici_id,
    dbtest_db.expertises.alici_id,
    dbtest_db.expertises.car_id,
    dbtest_db.expertises.km,
    dbtest_db.expertises.sase_no,
    dbtest_db.expertises.km_type,
    dbtest_db.expertises.net_agirlik,
    dbtest_db.expertises.nereden_ulastiniz,
    dbtest_db.expertises.sorgu_hizmeti,
    dbtest_db.expertises.belge_tarihi,
    dbtest_db.expertises.belge_no,
    dbtest_db.expertises.belge_ozel_kodu,
    dbtest_db.expertises.sigorta_teklif_ver,
    dbtest_db.expertises.yayin_yasagi,
    dbtest_db.expertises.kayit_yeri,
    dbtest_db.expertises.status,
    dbtest_db.expertises.odeme_turu,
    dbtest_db.expertises.bodywork_image,
    dbtest_db.expertises.diagnostic_file,
    dbtest_db.expertises.arac_kontrol_user,
    dbtest_db.expertises.arac_kontrol,
    dbtest_db.expertises.fren_kontrol_user,
    dbtest_db.expertises.fren_kontrol,
    dbtest_db.expertises.kaporta_kontrol_user,
    dbtest_db.expertises.kaporta_kontrol,
    dbtest_db.expertises.diagnostic_kontrol_user,
    dbtest_db.expertises.diagnostic_kontrol,
    dbtest_db.expertises.ic_kontrol_user,
    dbtest_db.expertises.ic_kontrol,
    dbtest_db.expertises.lastik_jant_kontrol_user,
    dbtest_db.expertises.lastik_jant_kontrol,
    dbtest_db.expertises.alt_motor_kontrol_user,
    dbtest_db.expertises.alt_motor_kontrol,
    dbtest_db.expertises.komponent_kontrol_user,
    dbtest_db.expertises.komponent_kontrol,
    dbtest_db.expertises.co2_kontrol_user,
    dbtest_db.expertises.co2_kontrol,
    dbtest_db.expertises.hasar_sorgu_sonuc,
    dbtest_db.expertises.kilometre_sorgu_sonuc,
    dbtest_db.expertises.borc_sorgu_sonuc,
    dbtest_db.expertises.ruhsat_sorgu_sonuc,
    dbtest_db.expertises.payment_type,
    dbtest_db.expertises.plus_card_payment_type,
    dbtest_db.expertises.plus_kart_id,
    dbtest_db.expertises.audio_url,
    dbtest_db.expertises.cikis_tarihi,
    dbtest_db.expertises.manuel_save,
    dbtest_db.expertises.employee_downloaded,
    dbtest_db.expertises.ftp_ok,
    dbtest_db.expertises.ftp_date,
    dbtest_db.expertises.created_at,
    dbtest_db.expertises.updated_at,
    dbtest_db.expertises.deleted_at
FROM dbtest_db.expertises
