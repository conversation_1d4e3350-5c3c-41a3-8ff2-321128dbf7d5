<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('payment_log', function (Blueprint $table) {
            $table->integer('id', true);
            $table->string('file_uuid')->nullable();
            $table->string('payment_id')->nullable();
            $table->string('payment_price')->nullable();
            $table->string('payment_session_id')->nullable();
            $table->string('payment_token_id')->nullable();
            $table->longText('payment_req')->nullable();
            $table->longText('payment_res')->nullable();
            $table->tinyInteger('payment_type')->nullable()->default(1)->comment('1 -) ekspertiz için 2-) plus kart ödeme için');
            $table->integer('status')->nullable()->default(0)->comment('0:Bekliyor || 1: Başarılı || 2:Başarısız');
            $table->timestamp('updated_at')->useCurrentOnUpdate()->useCurrent();
            $table->timestamp('created_at')->useCurrent();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('payment_log');
    }
};
