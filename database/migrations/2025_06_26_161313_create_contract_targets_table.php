<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('contract_targets', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('contract_id')->index('contract_targets_contract_id_foreign');
            $table->unsignedInteger('contract_period_id')->nullable();
            $table->string('date', 7)->nullable();
            $table->unsignedMediumInteger('min_count');
            $table->double('unit_price', 15, 2)->unsigned();
            $table->timestamps();
            $table->date('last_payment_date')->nullable();
            $table->unsignedTinyInteger('payment_completed')->default(0);
            $table->string('payment_type')->nullable();
            $table->softDeletes();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('contract_targets');
    }
};
