<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_card_campaign_usages', function (Blueprint $table) {
            $table->bigIncrements('id');
            $table->unsignedBigInteger('campaign_id');
            $table->unsignedBigInteger('plus_card_id')->index('plus_card_campaign_usages_plus_card_id_foreign');
            $table->unsignedBigInteger('expertise_id')->nullable()->index('plus_card_campaign_usages_expertise_id_foreign');
            $table->decimal('original_amount', 10);
            $table->decimal('discounted_amount', 10);
            $table->decimal('discount_value', 10);
            $table->timestamp('used_at');
            $table->timestamps();
            $table->softDeletes();

            $table->index(['campaign_id', 'plus_card_id', 'used_at']);
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('plus_card_campaign_usages');
    }
};
