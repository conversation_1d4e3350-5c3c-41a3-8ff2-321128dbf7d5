<?php

namespace Database\Seeders;

use Illuminate\Database\Seeder;
use App\Models\BranchCategoryUserLimit;

class BranchCategoryUserLimitSeeder extends Seeder
{
    /**
     * Run the database seeds.
     */
    public function run(): void
    {
        BranchCategoryUserLimit::truncate();

        $userLimits = [
            ['category_id' => 1, 'group_id' => 37, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 1, 'group_id' => 42, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 1, 'group_id' => 34, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 1, 'group_id' => 33, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 1, 'group_id' => 32, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 1, 'group_id' => 46, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 2, 'group_id' => 37, 'qty' => 2, 'status' => 1, 'sort' => 2],
            ['category_id' => 2, 'group_id' => 42, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 2, 'group_id' => 34, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 2, 'group_id' => 33, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 2, 'group_id' => 32, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 2, 'group_id' => 46, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 3, 'group_id' => 37, 'qty' => 3, 'status' => 1, 'sort' => 3],
            ['category_id' => 3, 'group_id' => 42, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 3, 'group_id' => 34, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 3, 'group_id' => 33, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 3, 'group_id' => 32, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 3, 'group_id' => 46, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 4, 'group_id' => 37, 'qty' => 1, 'status' => 1, 'sort' => 4],
            ['category_id' => 4, 'group_id' => 42, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 4, 'group_id' => 34, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 4, 'group_id' => 33, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 4, 'group_id' => 32, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 4, 'group_id' => 46, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 5, 'group_id' => 37, 'qty' => 10, 'status' => 1, 'sort' => 5],
            ['category_id' => 5, 'group_id' => 42, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 5, 'group_id' => 34, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 5, 'group_id' => 33, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 5, 'group_id' => 32, 'qty' => 5, 'status' => 1, 'sort' => 1],
            ['category_id' => 5, 'group_id' => 46, 'qty' => 5, 'status' => 1, 'sort' => 1],
        ];

        foreach ($userLimits as $limit) {
            BranchCategoryUserLimit::create($limit);
        }
    }
}
