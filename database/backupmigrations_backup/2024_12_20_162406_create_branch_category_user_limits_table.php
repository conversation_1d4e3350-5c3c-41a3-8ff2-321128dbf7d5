<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('branch_category_user_limits', function (Blueprint $table) {
            $table->id();
            $table->unsignedBigInteger('category_id');
            $table->unsignedBigInteger('group_id');
            $table->integer('qty')->default(0);
            $table->boolean('status')->default(true);
            $table->integer('sort')->default(0);
            $table->timestamps();

            // Foreign key constraints
            $table->foreign('category_id')->references('id')->on('branch_categories')->onDelete('cascade');
            $table->foreign('group_id')->references('id')->on('user_role_groups')->onDelete('cascade');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::dropIfExists('branch_category_user_limits');
    }
};
