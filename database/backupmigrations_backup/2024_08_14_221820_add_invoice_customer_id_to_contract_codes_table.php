<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('contract_codes', function (Blueprint $table) {
            $table->unsignedBigInteger('invoice_customer_id')->after('contract_id')->nullable();
            $table->foreign('invoice_customer_id')->references('id')->on('customers');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('contract_codes', function (Blueprint $table) {
            //
        });
    }
};
