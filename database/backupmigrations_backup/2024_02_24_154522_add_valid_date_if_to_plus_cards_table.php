<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('plus_cards', function (Blueprint $table) {
            $table->tinyInteger('valid_date_if')->after('puan')->default(0)->comment('0: False, 1: True');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('plus_cards', function (Blueprint $table) {
            //
        });
    }
};
