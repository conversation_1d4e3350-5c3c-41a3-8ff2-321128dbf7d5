<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::create('plus_card_agreement', function (Blueprint $table) {
            $table->id();
            $table->smallInteger('plus_card_id')->nullable();
            $table->smallInteger('customer_id')->nullable();
            $table->integer('unit_quantity')->nullable();
            $table->float('payment_amount',15,2)->nullable();
            $table->date('valid_date')->nullable();
            $table->integer('user_id')->nullable();
            $table->string('delayed_id', 7)->nullable();
            $table->smallInteger('kvkk_approval')->default(2);
            $table->smallInteger('agreement_approval')->default(2);
            $table->timestamps();
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        //
    }
};
