import * as React from "react";

function IconGenderGenderfluid({
  size = 24,
  color = "currentColor",
  stroke = 2,
  ...props
}) {
  return <svg xmlns="http://www.w3.org/2000/svg" className="icon icon-tabler icon-tabler-gender-genderfluid" width={size} height={size} viewBox="0 0 24 24" strokeWidth={stroke} stroke={color} fill="none" strokeLinecap="round" strokeLinejoin="round" {...props}><desc>{"Download more icon variants from https://tabler-icons.io/i/gender-genderfluid"}</desc><path stroke="none" d="M0 0h24v24H0z" fill="none" /><g transform="rotate(30 -2.778 22.523)"><circle cx={4.759} cy={6.021} r={4} /><path d="M8.758 6.02v-6" /><path d="M.758 12.02v-6" /></g><path d="M12 12h.01" /><path d="M9 9l-6 -6" /><path d="M5.5 8.5l3 -3" /><path d="M21 21l-6 -6" /><path d="M17 20l3 -3" /><path d="M3 7v-4h4" /></svg>;
}

export default IconGenderGenderfluid;