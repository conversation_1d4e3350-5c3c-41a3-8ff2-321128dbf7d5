import * as React from "react";

function IconDeviceMobileOff({
  size = 24,
  color = "currentColor",
  stroke = 2,
  ...props
}) {
  return <svg xmlns="http://www.w3.org/2000/svg" className="icon icon-tabler icon-tabler-device-mobile-off" width={size} height={size} viewBox="0 0 24 24" strokeWidth={stroke} stroke={color} fill="none" strokeLinecap="round" strokeLinejoin="round" {...props}><desc>{"Download more icon variants from https://tabler-icons.io/i/device-mobile-off"}</desc><path stroke="none" d="M0 0h24v24H0z" fill="none" /><path d="M7.174 3.178c.252 -.114 .531 -.178 .826 -.178h8a2 2 0 0 1 2 2v9m0 4v1a2 2 0 0 1 -2 2h-8a2 2 0 0 1 -2 -2v-13" /><path d="M11 4h2" /><path d="M12 17v.01" /><path d="M3 3l18 18" /></svg>;
}

export default IconDeviceMobileOff;