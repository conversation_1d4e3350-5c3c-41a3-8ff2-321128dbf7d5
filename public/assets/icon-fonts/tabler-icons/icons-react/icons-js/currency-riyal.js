import * as React from "react";

function IconCurrencyRiyal({
  size = 24,
  color = "currentColor",
  stroke = 2,
  ...props
}) {
  return <svg xmlns="http://www.w3.org/2000/svg" className="icon icon-tabler icon-tabler-currency-riyal" width={size} height={size} viewBox="0 0 24 24" strokeWidth={stroke} stroke={color} fill="none" strokeLinecap="round" strokeLinejoin="round" {...props}><desc>{"Download more icon variants from https://tabler-icons.io/i/currency-riyal"}</desc><path stroke="none" d="M0 0h24v24H0z" fill="none" /><path d="M15 9v2a2 2 0 1 1 -4 0v-1v1a2 2 0 1 1 -4 0v-1v4a2 2 0 1 1 -4 0v-2" /><path d="M18 12.01v-.01" /><path d="M22 10v1a5 5 0 0 1 -5 5" /></svg>;
}

export default IconCurrencyRiyal;